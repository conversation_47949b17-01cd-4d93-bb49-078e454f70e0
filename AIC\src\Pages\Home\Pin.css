/* Base styles (desktop) */
.pin-body {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f3f4f6;

}

.pin-container {
  text-align: center;
  font-family: Arial, sans-serif;
  color: #333;
  padding: 80px;
}

.pin-header {
  background-color: #d32f2f;
  color: white;
  padding: 160px 100px 220px;
  border-radius: 80px;
  max-width: 90%;
  margin: 0 auto;
  position: relative;
  min-height: 550px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  text-align: center;
}

.pin-header-content {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  text-align: center;
  max-width: 70%;
  width: 100%;
  margin: -30px auto 0;
  padding-top: 0;
}

.pin-header h1 {
  margin: 0;
  font-size: 2.5em;
  overflow-wrap: break-word;
}

.pin-header p {
  margin-top: 10px;
  font-size: 1.4em;
  line-height: 1.5;
  overflow-wrap: break-word;
}

.pin-side-image {
  position: absolute;
  top: 0;
  height: 100%;
  width: 30%;
  z-index: 1;
}

.pin-left-image {
  left: -24%;
  clip-path: polygon(80% 0, 100% 0, 100% 100%, 80% 100%);
}

.pin-right-image {
  right: -24%;
  clip-path: polygon(0 0, 20% 0, 20% 100%, 0 100%);
}

.pin-card-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 40px;
  position: relative;
  top: -130px;
}

.pin-card {
  background-color: white;
  padding: 15px;
  border-radius: 40px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  width: 360px;
  height: 600px;
  text-align: center;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 2;
}

.pin-icon {
  position: absolute;
  top: 10px;
  left: 40%;
  transform: translateX(-50%);
  width: auto;
  height: auto;
}

.pin-icon img {
  width: auto;
  height: auto;
}

.pin-card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.pin-card h3 {
  padding-top: 80px;
  font-size: 20px;
  margin: 10px 0;
  color: #d32f2f;
}

.pin-card p {
  font-size: 16px;
  line-height: 1.8;
  color: #666;
  margin: 10px 0;
  padding: 0 10px;
}

/* Tablet styles */
@media (max-width: 1023px) {
  .pin-container {
    padding: 40px;
  }

  .pin-header {
    padding: 80px 50px 160px;
    border-radius: 60px;
    min-height: 450px;
  }

  .pin-header-content {
    max-width: 80%;
  }

  .pin-header h1 {
    font-size: 2.2em;
  }

  .pin-header p {
    font-size: 1.2em;
  }

  .pin-side-image {
    width: 20%;
  }

  .pin-left-image {
    left: -15%;
  }

  .pin-right-image {
    right: -15%;
  }

  .pin-card-container {
    top: -100px;
    gap: 30px;
  }

  .pin-card {
    width: calc(50% - 15px);
    height: 500px;
  }
}

/* Mobile styles */
@media (max-width: 767px) {
  .pin-container {
    padding: 20px;
  }

  .pin-header {
    padding: 60px 30px 120px;
    border-radius: 40px;
    min-height: 350px;
  }

  .pin-header-content {
    max-width: 100%;
  }

  .pin-header h1 {
    font-size: 1.8em;
  }

  .pin-header p {
    font-size: 1em;
  }

  .pin-side-image {
    display: none;
  }

  .pin-card-container {
    top: -80px;
    gap: 20px;
  }

  .pin-card {
    width: 100%;
    height: auto;
    min-height: 400px;
  }

  .pin-card h3 {
    padding-top: 60px;
    font-size: 18px;
    margin: 5px 0;
  }

  .pin-card p {
    font-size: 14px;
    line-height: 1.6;
    margin: 5px 0;
  }
}