import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import sanityClient from '../../sanity';
import { PortableText } from '@portabletext/react';
import { ArrowLeft, ArrowRight, ExternalLink, Calendar, Globe, Tag } from 'lucide-react';
import imageUrlBuilder from '@sanity/image-url';
import SEO from '../../components/SEO';
import { useLanguage } from '../../components/languageUtils';

const builder = imageUrlBuilder(sanityClient);

function urlFor(source) {
  return builder.image(source);
}

const ProjectsDetails = () => {
  const { slug } = useParams();
  const [project, setProject] = useState(null);
  const [nextProject, setNextProject] = useState(null);
  const [prevProject, setPrevProject] = useState(null);
  const [news, setNews] = useState([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [currentNewsIndex, setCurrentNewsIndex] = useState(0);
  const navigate = useNavigate();
  const { language } = useLanguage();

  useEffect(() => {
    sanityClient
      .fetch(
        `*[_type == "projectsPost" && slug.current == $slug]{
          title,
          language,
          slug,
          projectDate,
          mainImage{
            asset->{
              _id,
              url
            },
            alt
          },
          gallery[]{asset->{_id, url}},
          pdfFiles[]{
            asset->{url},
            customName
          },
          body[]{
            ...,
            _type == "image" => {
              ...,
              asset->{
                _id,
                url
              }
            }
          },
          tags[]->{
            title
          },
          category,
          metaTitle,
          metaDescription,
          "next": *[_type == "projectsPost" && language == ^.language && dateTime(projectDate) > dateTime(^.projectDate)] | order(projectDate asc) [0]{ title, slug },
          "previous": *[_type == "projectsPost" && language == ^.language && dateTime(projectDate) < dateTime(^.projectDate)] | order(projectDate desc) [0]{ title, slug }
        }`,
        { slug }
      )
      .then((data) => {
        console.log("Fetched project data:", data[0]);
        setProject(data[0]);
        setNextProject(data[0]?.next);
        setPrevProject(data[0]?.previous);
        window.scrollTo(0, 0);

        sanityClient
          .fetch(
            `*[_type == "newsPost" && language == $language]{
              title,
              slug,
              mainImage{
                asset->{
                  _id,
                  url
                },
                alt
              },
              publishDate,
              body
            }`,
            { language: data[0].language }
          )
          .then((newsData) => setNews(newsData))
          .catch(console.error);
      })
      .catch(console.error);
  }, [slug]);

  const handleNavigation = (projectSlug) => {
    navigate(`/projects/${projectSlug}`);
    window.scrollTo(0, 0);
  };

  const nextImage = () => {
    setCurrentImageIndex((prevIndex) =>
      prevIndex === project.gallery.length - 1 ? 0 : prevIndex + 1
    );
  };

  const prevImage = () => {
    setCurrentImageIndex((prevIndex) =>
      prevIndex === 0 ? project.gallery.length - 1 : prevIndex - 1
    );
  };

  const nextNews = () => {
    setCurrentNewsIndex((prevIndex) =>
      prevIndex === news.length - 1 ? 0 : prevIndex + 1
    );
  };

  const prevNews = () => {
    setCurrentNewsIndex((prevIndex) =>
      prevIndex === 0 ? news.length - 1 : prevIndex - 1
    );
  };

  const limitTitleLength = (title, limit = 20) => {
    return title.length > limit ? title.substring(0, limit) + '...' : title;
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const getLanguageDisplay = (langCode) => {
    return langCode === 'sq' ? 'Shqip' : 'English';
  };

  const getLocalizedProjectsPrefix = (language) => {
    return language === 'en' ? 'projects' : 'projekte';
  };

  const getLocalizedNewsPrefix = (language) => {
    return language === 'en' ? 'news' : 'lajme';
  };

  const truncateText = (text, maxLength) => {
    if (!text) return '';
    return text.length <= maxLength ? text : text.substr(0, maxLength) + '...';
  };

  // Modern Portable Text components - much cleaner and more reliable
  const portableTextComponents = {
    types: {
      image: ({value}) => {
        if (!value?.asset) return null;
        
        const imageUrl = value.asset.url || urlFor(value).width(1500).url();
        
        return (
          <figure className="my-8">
            <img
              src={imageUrl}
              alt={value.alt || ''}
              className="w-full rounded-lg shadow-lg"
              loading="lazy"
            />
            {value.caption && (
              <figcaption className="text-center text-sm text-gray-600 mt-2">
                {value.caption}
              </figcaption>
            )}
          </figure>
        );
      },
    },
    block: {
      normal: ({children}) => <p className="mb-4 text-gray-700 leading-relaxed">{children}</p>,
      h1: ({children}) => <h1 className="text-4xl font-bold text-gray-800 mt-8 mb-4">{children}</h1>,
      h2: ({children}) => <h2 className="text-3xl font-bold text-gray-800 mt-6 mb-3">{children}</h2>,
      h3: ({children}) => <h3 className="text-2xl font-bold text-gray-800 mt-5 mb-3">{children}</h3>,
      h4: ({children}) => <h4 className="text-xl font-bold text-gray-800 mt-4 mb-2">{children}</h4>,
      h5: ({children}) => <h5 className="text-lg font-bold text-gray-800 mt-4 mb-2">{children}</h5>,
      h6: ({children}) => <h6 className="text-base font-bold text-gray-800 mt-3 mb-2">{children}</h6>,
      blockquote: ({children}) => (
        <blockquote className="border-l-4 border-red-600 pl-6 italic text-gray-700 my-6 bg-gray-50 py-4 rounded-r-lg">
          {children}
        </blockquote>
      ),
    },
    list: {
      bullet: ({children}) => <ul className="list-disc pl-6 mb-4 space-y-2">{children}</ul>,
      number: ({children}) => <ol className="list-decimal pl-6 mb-4 space-y-2">{children}</ol>,
    },
    listItem: {
      bullet: ({children}) => <li className="text-gray-700 leading-relaxed">{children}</li>,
      number: ({children}) => <li className="text-gray-700 leading-relaxed">{children}</li>,
    },
    marks: {
      strong: ({children}) => <strong className="font-bold">{children}</strong>,
      em: ({children}) => <em className="italic">{children}</em>,
      underline: ({children}) => <span className="underline">{children}</span>,
      'strike-through': ({children}) => <span className="line-through">{children}</span>,
      link: ({value, children}) => {
        const target = value?.blank ? '_blank' : undefined;
        const rel = target === '_blank' ? 'noopener noreferrer' : undefined;
        
        return (
          <a 
            href={value?.href} 
            target={target} 
            rel={rel}
            className="text-red-600 hover:text-red-800 underline transition-colors duration-200"
          >
            {children}
          </a>
        );
      },
    },
  };

  if (!project) return <div className="text-center py-10">Loading...</div>;

  const projectsPrefix = getLocalizedProjectsPrefix(project.language);
  const currentUrl = `${window.location.origin}/${project.language}/${projectsPrefix}/${project.slug.current}`;

  return (
    <>
      <SEO
        post={project}
        ogUrl={currentUrl}
        ogImage={project.mainImage?.asset?.url}
        ogType="article"
        language={project.language}
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10 flex flex-col lg:flex-row lg:justify-between">
        <div className="flex-1 max-w-3xl bg-white rounded-lg shadow-md p-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-5 pb-2 border-b-2 border-red-600">
            {project.title}
          </h1>
          
          <div className="flex items-center text-sm text-gray-600 mb-5">
            <Calendar size={16} className="mr-2" />
            <span className="mr-4">{formatDate(project.projectDate)}</span>
            <Globe size={16} className="mr-2" />
            <span>{getLanguageDisplay(project.language)}</span>
          </div>

          {project.mainImage?.asset?.url && (
            <img
              src={project.mainImage.asset.url}
              alt={project.mainImage.alt || project.title}
              className="w-full rounded-lg mb-8"
            />
          )}

          {/* Modern Portable Text rendering - much cleaner */}
          <div className="prose prose-lg max-w-none mb-8">
            <PortableText
              value={project.body || []}
              components={portableTextComponents}
            />
          </div>

          {project.pdfFiles && project.pdfFiles.length > 0 && (
            <div className="bg-gray-100 border border-gray-300 rounded-lg p-6 mt-8">
              <h3 className="text-xl font-semibold text-gray-800 mb-4">
                {project.language === 'en' ? 'Related Documents' : 'Dokumente të Lidhura'}
              </h3>
              <div className="space-y-4">
                {project.pdfFiles.map((pdfFile, index) => (
                  <div key={index} className="flex flex-col sm:flex-row gap-4 items-center">
                    <div className="bg-red-600 text-white py-2 px-4 rounded-md font-semibold">
                      <span>{pdfFile.customName || `PDF ${index + 1}`}</span>
                    </div>
                    <a 
                      href={pdfFile.asset.url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="flex items-center bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-md transition duration-300"
                    >
                      <ExternalLink size={20} className="mr-2" />
                      <span>{project.language === 'en' ? 'View PDF' : 'Shiko PDF'}</span>
                    </a>
                  </div>
                ))}
              </div>
            </div>
          )}

          {project.tags && project.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mt-8">
              <Tag size={20} className="text-red-600 mr-2" />
              {project.tags.map((tag, index) => (
                <span 
                  key={index}
                  className="bg-gray-200 text-red-600 px-3 py-1 rounded-full text-sm transition duration-300 hover:bg-red-600 hover:text-white cursor-default"
                >
                  {tag.title}
                </span>
              ))}
            </div>
          )}

          {project.gallery && project.gallery.length > 0 && (
            <div className="mt-10 mb-10">
              <h3 className="text-2xl text-gray-800 mb-5">
                {project.language === 'en' ? 'Gallery' : 'Galeria'}
              </h3>
              <div className="relative h-96 bg-gray-100 rounded-lg overflow-hidden">
                <button 
                  onClick={prevImage} 
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-red-600 bg-opacity-70 hover:bg-opacity-90 text-white rounded-full p-2 transition duration-300 z-10"
                >
                  <ArrowLeft size={20} />
                </button>
                <div className="w-full h-full flex justify-center items-center">
                  <img
                    src={project.gallery[currentImageIndex].asset.url}
                    alt={`Gallery item ${currentImageIndex + 1}`}
                    className="max-w-full max-h-full object-contain"
                  />
                </div>
                <button 
                  onClick={nextImage} 
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-red-600 bg-opacity-70 hover:bg-opacity-90 text-white rounded-full p-2 transition duration-300 z-10"
                >
                  <ArrowRight size={20} />
                </button>
              </div>
              <p className="text-center mt-2 text-gray-600 italic">
                {project.language === 'en' 
                  ? `Image ${currentImageIndex + 1} of ${project.gallery.length}`
                  : `Foto ${currentImageIndex + 1} nga ${project.gallery.length}`
                }
              </p>
            </div>
          )}

          <div className="flex flex-col sm:flex-row justify-between gap-4 mt-10">
            {prevProject && (
              <button 
                onClick={() => handleNavigation(prevProject.slug.current)} 
                className="flex items-center justify-center sm:justify-start bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-full transition duration-300 transform hover:-translate-y-1"
              >
                <ArrowLeft size={20} className="mr-2" />
                <span>
                  {project.language === 'en' ? 'Previous: ' : 'Mëparshëm: '}
                  {limitTitleLength(prevProject.title)}
                </span>
              </button>
            )}
            {nextProject && (
              <button 
                onClick={() => handleNavigation(nextProject.slug.current)} 
                className="flex items-center justify-center sm:justify-end bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-full transition duration-300 transform hover:-translate-y-1"
              >
                <span>
                  {project.language === 'en' ? 'Next: ' : 'Tjetër: '}
                  {limitTitleLength(nextProject.title)}
                </span>
                <ArrowRight size={20} className="ml-2" />
              </button>
            )}
          </div>
        </div>

        <div className="w-full lg:w-80 mt-10 lg:mt-0 lg:ml-8">
          <div className="sticky top-5 bg-white rounded-lg shadow-md p-6">
            <h2 className="text-2xl text-gray-800 mb-5 pb-2 border-b-2 border-red-600">
              {project.language === 'en' ? 'Latest News' : 'Lajmet e Fundit'}
            </h2>
            {news.length > 0 ? (
              <div>
                {news[currentNewsIndex].mainImage?.asset?.url && (
                  <img
                    src={news[currentNewsIndex].mainImage.asset.url}
                    alt={news[currentNewsIndex].mainImage.alt || news[currentNewsIndex].title}
                    className="w-full h-48 object-cover rounded-lg mb-4"
                  />
                )}
                <div>
                  <h3 className="text-xl text-gray-800 mb-2">
                    {truncateText(news[currentNewsIndex].title, 30)}
                  </h3>
                  {news[currentNewsIndex].publishDate && (
                    <p className="text-sm text-gray-600 mb-2">
                      Date: {formatDate(news[currentNewsIndex].publishDate)}
                    </p>
                  )}
                  <div className="text-sm text-gray-600 mb-4">
                    {truncateText(news[currentNewsIndex].body?.[0]?.children?.[0]?.text || '', 60)}
                  </div>
                  <a 
                    href={`/${project.language}/${getLocalizedNewsPrefix(project.language)}/${news[currentNewsIndex].slug.current}`} 
                    className="inline-block bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded text-sm font-semibold transition duration-300"
                  >
                    {project.language === 'en' ? 'Read More' : 'Lexo më shumë'}
                  </a>
                </div>
                <div className="flex justify-between mt-4">
                  <button onClick={prevNews} className="text-red-600 hover:text-red-700 transition duration-300">
                    <ArrowLeft size={20} />
                  </button>
                  <button onClick={nextNews} className="text-red-600 hover:text-red-700 transition duration-300">
                    <ArrowRight size={20} />
                  </button>
                </div>
              </div>
            ) : (
              <p className="text-gray-600">No news available in {getLanguageDisplay(project.language)}.</p>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default ProjectsDetails;