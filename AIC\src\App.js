// App.js
import React, { useEffect, useState, useContext } from 'react';
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
  useLocation,
  useNavigate,
} from 'react-router-dom';
import GoogleAnalyticsScript from './GoogleAnalyticsScript';
import ReactGA from 'react-ga4'; // Import GA4
import { LanguageProvider, useLanguage, translateRoute } from './components/languageUtils';
import { GlobalLanguageProvider, GlobalLanguageContext } from './components/LanguageSwitcher';
import MainHeader from './components/MainHeader';
import HeaderHome from './Pages/Home/HeaderHome';
import Footer from './components/Footer';
import Loading from './components/Loading';
import { HelmetProvider } from 'react-helmet-async';


// Import Map component
import Map from './components/map/Map';

import Home from './Pages/Home/Home';
import About from './Pages/About/About';
import KonsulencaAic from './Pages/About/KonsulencaAic';
import WhyChooseAIC from './Pages/About/WhyChooseAIC';
import ByLaw from './Pages/About/ByLaw';
import Transparency from './Pages/About/Transparency';
import Contact from './Pages/Contact/Contact';
import Projects from './Pages/Projects/Projects';
import StrategicProjects from './Pages/Projects/StrategicProjects';
import RevitalizationProjects from './Pages/Projects/RevitalizationProjects';
import RealEstateProjects from './Pages/Projects/RealEstateProjects';
import CoastalAreasProjects from './Pages/Projects/CoastalAreasProjects';
import DowntownAreasProjects from './Pages/Projects/DowntownAreasProjects';
import PublicServiceProjects from './Pages/Projects/PublicServiceProjects';
import Ongoing from './Pages/Open Calls/Ongoing';
import ClosedCalls from './Pages/Open Calls/ClosedCalls';
import ActiveCalls from './Pages/Open Calls/ActiveCalls';
import UpcomingCalls from './Pages/Open Calls/UpcomingCalls'; // New import for UpcomingCalls
import MeetTheStaff from './Pages/Team/MeetTheStaff';
import Bio from './Pages/Team/Bio';
import InstitutionalStructure from './Pages/Team/InstitutionalStructure';
import AICRelationshipPage from './Pages/Networking/AICRelationshipPage';
import NewsPage from './Pages/News/NewsPage';
import JoinUs from './Pages/Team/JoinUs';
import NewsDetails from './Pages/News/NewsDetails';
import TagPages from './Pages/News/TagPages';
import ProjectsDetails from './Pages/Projects/ProjectsDetails';
import WelcomePopup from './components/Popup'; // Changed to import the proper component
import NotFoundPage from './components/404';
import OpenData from './Pages/Opendata/OpenData';

// Initialize Google Analytics
ReactGA.initialize('G-VDJHDXTWZE');

const ConditionalHeader = () => {
  const location = useLocation();
  const isHomePage = (pathname) => {
    return ['/', '/sq', '/en', '/sq/', '/en/'].includes(pathname) ||
           /^\/(?:sq|en)\/?$/.test(pathname);
  };
  
  // Don't show header on map page
  const isMapPage = (pathname) => {
    return pathname.includes('/map') || pathname.includes('/harta');
  };
  
  if (isMapPage(location.pathname)) {
    return null; // Don't render any header on map page
  }
  
  return isHomePage(location.pathname) ? <HeaderHome /> : <MainHeader />;
};

const App = () => {
  const { language, changeLanguage } = useLanguage();
  const { globalLanguage, setGlobalLanguage } = useContext(GlobalLanguageContext);
  const location = useLocation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  // Track page views when route changes
  useEffect(() => {
    ReactGA.send({ hitType: "pageview", page: location.pathname });
  }, [location]);

  useEffect(() => {
    const currentLang = location.pathname.split('/')[1];
    if (currentLang === 'sq' || currentLang === 'en') {
      changeLanguage(currentLang);
      setGlobalLanguage(currentLang);
    } else {
      navigate(`/sq${location.pathname}`, { replace: true });
    }
  }, [location, changeLanguage, setGlobalLanguage, navigate]);

  useEffect(() => {
    // Skip loading screen for map page
    if (location.pathname.includes('/map') || location.pathname.includes('/harta')) {
      setLoading(false);
      return;
    }
    
    setLoading(true);
    const timer = setTimeout(() => setLoading(false), 700);
    return () => clearTimeout(timer);
  }, [location]);

  if (loading) {
    return (
      <>
        <ConditionalHeader />
        <Loading />
      </>
    );
  }

  return (
    <>
    
      <ConditionalHeader />
      <Routes>
        <Route path="/" element={<Navigate to="/sq" replace />} />
        <Route path="/:lang" element={<Home />} />
        <Route path="/:lang/" element={<Home />} />
        <Route path={`/:lang/${translateRoute('about-us/vision-and-mission', 'en', language)}`} element={<About />} />
        <Route path={`/:lang/${translateRoute('about-us/why-choose-aic', 'en', language)}`} element={<WhyChooseAIC />} />
        <Route path={`/:lang/${translateRoute('about-us/law-and-regulations', 'en', language)}`} element={<ByLaw />} />
        <Route path={`/:lang/${translateRoute('about-us/consultancy', 'en', language)}`} element={<KonsulencaAic />} />
        <Route path={`/:lang/${translateRoute('transparency', 'en', language)}`} element={<Transparency />} />
        <Route path={`/:lang/${translateRoute('open-data', 'en', language)}`} element={<OpenData />} />
        <Route path={`/:lang/${translateRoute('contact', 'en', language)}`} element={<Contact />} />
        <Route path={`/:lang/${translateRoute('team/meet-the-staff', 'en', language)}`} element={<MeetTheStaff />} />
        <Route path={`/:lang/${translateRoute('team/join-us', 'en', language)}`} element={<JoinUs />} />
        <Route path={`/:lang/${translateRoute('team/institutional-structure', 'en', language)}`} element={<InstitutionalStructure />} />
        
        {/* Project routes */}
        <Route path={`/:lang/${translateRoute('projects', 'en', language)}`} element={<Projects />} />
        
        {/* New project category routes */}
        <Route path={`/:lang/${translateRoute('projects/strategic-projects', 'en', language)}`} element={<StrategicProjects />} />
        <Route path={`/:lang/${translateRoute('projects/revitalization', 'en', language)}`} element={<RevitalizationProjects />} />
        <Route path={`/:lang/${translateRoute('projects/real-estate', 'en', language)}`} element={<RealEstateProjects />} />
        
        {/* New project subcategory routes */}
        <Route path={`/:lang/${translateRoute('projects/real-estate/coastal-areas', 'en', language)}`} element={<CoastalAreasProjects />} />
        <Route path={`/:lang/${translateRoute('projects/real-estate/downtown-areas', 'en', language)}`} element={<DowntownAreasProjects />} />
        <Route path={`/:lang/${translateRoute('projects/real-estate/public-service-complex', 'en', language)}`} element={<PublicServiceProjects />} />
        
        {/* Map Component Route */}
        <Route path={`/:lang/${translateRoute('map', 'en', language)}`} element={<Map />} />
        <Route path={`/:lang/map`} element={<Map />} />
        
        {/* Updated Calls Routes */}
        <Route path={`/:lang/${translateRoute('calls', 'en', language)}`} element={<Navigate to={`/${language}/${translateRoute('calls/open-calls', 'en', language)}`} replace />} />
        <Route path={`/:lang/${translateRoute('calls/open-calls', 'en', language)}`} element={<ActiveCalls />} />
        <Route path={`/:lang/${translateRoute('calls/upcoming-calls', 'en', language)}`} element={<UpcomingCalls />} />
        <Route path={`/:lang/${translateRoute('calls/ongoing', 'en', language)}`} element={<Ongoing />} />
        <Route path={`/:lang/${translateRoute('calls/closed', 'en', language)}`} element={<ClosedCalls />} />
        
        {/* Redirects from old routes */}
        <Route path={`/:lang/${translateRoute('open-calls/ongoing', 'en', language)}`} element={<Navigate to={`/${language}/${translateRoute('calls/ongoing', 'en', language)}`} replace />} />
        <Route path={`/:lang/${translateRoute('open-calls/closed', 'en', language)}`} element={<Navigate to={`/${language}/${translateRoute('calls/closed', 'en', language)}`} replace />} />
        
        <Route path={`/:lang/${translateRoute('networking', 'en', language)}`} element={<AICRelationshipPage />} />
        <Route path={`/:lang/${translateRoute('news', 'en', language)}`} element={<NewsPage />} />
        <Route path={`/:lang/${translateRoute('team/meet-the-staff', 'en', language)}/:slug`} element={<Bio />} />
        <Route path={`/:lang/${translateRoute('news', 'en', language)}/:slug`} element={<NewsDetails />} />
        <Route path={`/:lang/${translateRoute('projects', 'en', language)}/:slug`} element={<ProjectsDetails />} />
        <Route path={`/:lang/${translateRoute('tags', 'en', language)}/:slug`} element={<TagPages />} />

        {/* 404 fallback route */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
      
      {/* Add the WelcomePopup component here */}
      <WelcomePopup />
      
      {/* Don't show footer on map page */}
      {!location.pathname.includes('/map') && !location.pathname.includes('/harta') && <Footer />}
    </>
  );
};

const AppWrapper = () => {
  return (
  <HelmetProvider>
    <Router>
      <GoogleAnalyticsScript />
      <GlobalLanguageProvider>
        <LanguageProvider>
          <App />
        </LanguageProvider>
      </GlobalLanguageProvider>
    </Router>
</HelmetProvider>

  );
};

export default AppWrapper;