.project-container {
    position: relative;
    background-size: cover;
    background-position: center;
    color: white;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
  
.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1;
}
  
.project-content {
    position: relative;
    z-index: 2;
    padding: 20px;
    text-align: center;
}
  
.project-red-accent {
    width: 100px;
    height: 10px;
    background-color: #e41e26;
    margin: 0 auto 20px auto;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
  
/* Base styles for heading containers to ensure text is centered properly */
.project-content h1, .project-content h2 {
    text-align: center;
    color: white; /* Ensures the text is white */
    position: relative; /* Needed for pseudo-elements */
    margin: 20px 0; /* Adjust spacing to your preference */
}

/* Specific styles for the main heading (h1) */
.project-content h1 {
    font-size: 32px; /* Adjust size as needed */
    font-weight: bold; /* Makes text bold */
    margin-bottom: 10px; /* Space below the h1 before the red bar */
}



/* Styles for the secondary heading (h2) */
.project-content h2 {
    font-size: 24px; /* Smaller than h1 */
    font-weight: normal; /* Less emphasis than h1 */
}

/* Optional: Text shadow for better readability over varied backgrounds */
.project-content h1, .project-content h2 {
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.6);
}
.project-content p {
    text-align: center; /* Centers the text */
    font-size: 16px; /* Adjust font size as needed */
    font-weight: 500; /* Makes the paragraph text lighter */
    color: white; /* Ensures the text is white */
    line-height: 1.6; /* Increases readability with adequate line height */
    margin: 10px auto 20px; /* Adds spacing above and below the paragraph */
    max-width: 1200px; /* Limits the width for better readability */
}

/* Optional: Add a subtle text shadow for better readability over images */
.project-content p {
    text-shadow: 1px 1px 6px rgba(0, 0, 0, 0.5); /* Subtle shadow effect */
}
.project-slider {
    position: relative;
    width: 100%;
    max-width: 1500px;
    margin: 20px auto;
    overflow: hidden;
    height: 750px;
    padding-bottom: 0px;
}
  
.project-slides-container {
    display: flex;
    transition: transform 0.5s ease;
    height: 100%;
}
  
.project-slide {
    flex: 0 0 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: transform 0.5s ease;
    height: 100%;
    padding: 50px;
}
  
.project {
    width: 100%;
    height: 100%;
    border-radius: 60px;
    background-size: cover;
    background-position: center;
    overflow: hidden;
    box-shadow: 0 10px 30px -10px rgba(0, 0, 0, 0.3);
    transition: box-shadow 0.3s ease, transform 0.3s ease;
}
  
.project-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    color: white;
    font-size: 60px;
    cursor: pointer;
    padding: 20px;
    z-index: 10;
}
  
.left {
    left: -20px;
}
  
.right {
    right: -20px;
}
  
.project-investment-cta {
    background-color: #e41e26;
    color: white;
    padding: 40px 20px;
    text-align: center;
    margin-top: 0;
}
  
.project-investment-cta h3 {
    margin-bottom: 20px;
    font-size: 24px;
}
  
.project-invest-button {
    background-color: white;
    color: #e41e26;
    border: none;
    padding: 10px 20px;
    font-size: 18px;
    font-weight: bold;
    border-radius: 25px;
    cursor: pointer;
    transition: background-color 0.3s, color 0.3s;
}
  
.project-invest-button:hover {
    background-color: #ff3333;
    color: white;
}

/* ... (previous styles remain the same) */

.no-open-calls {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 30px;
  margin: 20px auto;
  max-width: 600px;
  backdrop-filter: blur(5px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.no-open-calls h3 {
  font-size: 24px;
  color: #e41e26;
  margin-bottom: 15px;
}

.no-open-calls p {
  font-size: 16px;
  color: white;
  line-height: 1.6;
}

/* ... (rest of the styles remain the same) */

@media (max-width: 1024px) {
    .project-slider {
        height: 50vh;
        padding-bottom: 0;
    }
  
    .project-slide {
        flex: 0 0 50%;
        padding: 10px;
    }
  
    .project {
        border-radius: 30px;
    }
  
    .project-arrow {
        display: none;
    }
}
  
@media (max-width: 768px) {
    .project-slider {
        height: 100vh;
        max-width: 100%;
        margin: 20px 0;
    }
  
    .project-slide {
        flex: 0 0 100%;
        padding: 10px;
    }
  
    .project {
        width: 100%;
        height: 100%;
        border-radius: 20px;
    }
}
  
@media (max-width: 480px) {
    .project-slider {
        height: 50vh;
    }
  
    .project {
        border-radius: 15px;
    }
}
