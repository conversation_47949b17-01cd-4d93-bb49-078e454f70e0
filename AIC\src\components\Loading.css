/* Loading.css */
.Loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #ffffff; /* White background */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.Loading-animation {
  display: flex;
  gap: 10px; /* Space between dots */
}

.Loading-dot {
  width: 15px;
  height: 15px;
  background-color: #ff4500; /* Vibrant red dot color */
  border-radius: 50%;
  animation: bounce 1.4s infinite ease-in-out both;
}

@keyframes bounce {
  0%, 100% {
    transform: scale(0);
  }
  50% {
    transform: scale(1);
  }
}

.Loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.Loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

.Loading-dot:nth-child(3) {
  animation-delay: 0s;
}

.Loading-dot:nth-child(4) {
  animation-delay: 0.16s;
}
