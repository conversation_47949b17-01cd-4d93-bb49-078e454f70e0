// src/App.js
import React from 'react';
import { useLanguage } from '../../components/languageUtils';
import SEO from '../../components/SEO';
import { seoTitles } from '../../config/seoTitles';
import ProjectsShowcase from "./ProjectsShowcase";

import './Projects.css';


function Contact() {
  const { language } = useLanguage();
  const seoData = seoTitles.projects[language];

  return (
    <>
      <SEO
        customTitle={seoData.title}
        customDescription={seoData.description}
        language={language}
        ogType="website"
      />
      <div className="App">
        <ProjectsShowcase/>
      </div>
    </>
  );
}

export default Contact;