// schemas/welcomePopup.ts
import { defineType, defineField } from 'sanity'

// Simple type for fields that have both English and Albanian versions
interface LocaleStringField {
  en: string
  sq: string
}

export default defineType({
  name: 'welcomePopup',
  title: 'Welcome Popup',
  type: 'document',
  fields: [
    defineField({
      name: 'title',
      title: 'Popup Name',
      type: 'string',
      description: 'Name for this popup (only visible in the admin panel)',
      validation: Rule => Rule.required(),
    }),
    defineField({
      name: 'enabled',
      title: 'Turn Popup On/Off',
      type: 'boolean',
      initialValue: true,
    }),
    defineField({
      name: 'displayFrequency',
      title: 'Days Between Showings',
      description: 'How many days to wait before showing the popup again to the same visitor',
      type: 'number',
      validation: Rule => Rule.required().integer().positive(),
      initialValue: 7,
    }),
    defineField({
      name: 'displayDelay',
      title: 'Waiting Time (milliseconds)',
      description: 'How long to wait after page loads before showing the popup',
      type: 'number',
      validation: Rule => Rule.required().integer().positive(),
      initialValue: 800,
    }),
    defineField({
      name: 'projects',
      title: 'Projects to Show',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            defineField({
              name: 'name',
              title: 'Project Name',
              type: 'object',
              fields: [
                defineField({
                  name: 'en',
                  title: 'English Name',
                  type: 'string',
                  validation: Rule => Rule.required()
                }),
                defineField({
                  name: 'sq',
                  title: 'Albanian Name',
                  type: 'string',
                  validation: Rule => Rule.required()
                })
              ]
            }),
            defineField({
              name: 'subtitle',
              title: 'Short Description',
              type: 'object',
              fields: [
                defineField({
                  name: 'en',
                  title: 'English Description',
                  type: 'text',
                  rows: 2,
                  validation: Rule => Rule.required()
                }),
                defineField({
                  name: 'sq',
                  title: 'Albanian Description',
                  type: 'text',
                  rows: 2,
                  validation: Rule => Rule.required()
                })
              ]
            }),
            defineField({
              name: 'date',
              title: 'Project Date',
              type: 'date',
              options: {
                dateFormat: 'DD MMMM YYYY',
              }
            }),
            defineField({
              name: 'path',
              title: 'Link to Project Page',
              description: 'The URL path where visitors will go when they click (without language prefix)',
              type: 'object',
              fields: [
                defineField({
                  name: 'en',
                  title: 'English Path',
                  type: 'string',
                  validation: Rule => Rule.required()
                }),
                defineField({
                  name: 'sq',
                  title: 'Albanian Path',
                  type: 'string',
                  validation: Rule => Rule.required()
                })
              ]
            }),
            defineField({
              name: 'mediaType',
              title: 'Display Type',
              type: 'string',
              options: {
                list: [
                  { title: 'Image', value: 'image' },
                  { title: 'Video', value: 'video' }
                ],
                layout: 'radio'
              },
              initialValue: 'image'
            }),
            defineField({
              name: 'image',
              title: 'Project Images',
              type: 'object',
              fields: [
                defineField({
                  name: 'en',
                  title: 'English Image',
                  type: 'image',
                  options: { hotspot: true }
                }),
                defineField({
                  name: 'sq',
                  title: 'Albanian Image',
                  type: 'image',
                  options: { hotspot: true }
                })
              ],
              hidden: ({parent}) => parent?.mediaType !== 'image'
            }),
            defineField({
              name: 'video',
              title: 'Upload Videos',
              type: 'object',
              fields: [
                defineField({
                  name: 'en',
                  title: 'English Video File',
                  type: 'file',
                  options: {
                    accept: 'video/*'
                  }
                }),
                defineField({
                  name: 'sq',
                  title: 'Albanian Video File',
                  type: 'file',
                  options: {
                    accept: 'video/*'
                  }
                })
              ],
              hidden: ({parent}) => parent?.mediaType !== 'video'
            }),
            defineField({
              name: 'videoUrl',
              title: 'Video Links',
              description: 'If you prefer to link to videos instead of uploading them',
              type: 'object',
              fields: [
                defineField({
                  name: 'en',
                  title: 'English Video Link',
                  type: 'url'
                }),
                defineField({
                  name: 'sq',
                  title: 'Albanian Video Link',
                  type: 'url'
                })
              ],
              hidden: ({parent}) => parent?.mediaType !== 'video'
            }),
            defineField({
              name: 'videoThumbnail',
              title: 'Video Preview Images',
              description: 'Images shown before video plays',
              type: 'object',
              fields: [
                defineField({
                  name: 'en',
                  title: 'English Preview Image',
                  type: 'image',
                  options: { hotspot: true }
                }),
                defineField({
                  name: 'sq',
                  title: 'Albanian Preview Image',
                  type: 'image',
                  options: { hotspot: true }
                })
              ],
              hidden: ({parent}) => parent?.mediaType !== 'video'
            })
          ],
          preview: {
            select: {
              title: 'name.en',
              subtitle: 'subtitle.en',
              media: 'mediaType',
              imageEn: 'image.en',
              thumbEn: 'videoThumbnail.en'
            },
            prepare(selection) {
              const { title, subtitle, media, imageEn, thumbEn } = selection;
              return {
                title: title || 'Unnamed Project',
                subtitle: subtitle,
                media: media === 'image' ? imageEn : thumbEn
              };
            }
          }
        }
      ],
      validation: Rule => Rule.required().min(1).error('Add at least one project')
    }),
    defineField({
      name: 'uiText',
      title: 'Button and Hint Text',
      type: 'object',
      fields: [
        defineField({
          name: 'buttonText',
          title: 'Learn More Button',
          type: 'object',
          fields: [
            defineField({
              name: 'en',
              title: 'English Button Text',
              type: 'string',
              initialValue: 'Learn More',
              validation: Rule => Rule.required()
            }),
            defineField({
              name: 'sq',
              title: 'Albanian Button Text',
              type: 'string',
              initialValue: 'Mëso më shumë',
              validation: Rule => Rule.required()
            })
          ]
        }),
        defineField({
          name: 'switchText',
          title: 'Next Project Button',
          type: 'object',
          fields: [
            defineField({
              name: 'en',
              title: 'English Next Button Text',
              type: 'string',
              initialValue: 'View Next Project',
              validation: Rule => Rule.required()
            }),
            defineField({
              name: 'sq',
              title: 'Albanian Next Button Text',
              type: 'string',
              initialValue: 'Shiko Projektin Tjetër',
              validation: Rule => Rule.required()
            })
          ]
        }),
        defineField({
          name: 'hintText',
          title: 'Hint Message',
          description: 'Text shown in the floating hint bubble',
          type: 'object',
          fields: [
            defineField({
              name: 'en',
              title: 'English Hint',
              type: 'string',
              initialValue: 'Check our latest development opportunities',
              validation: Rule => Rule.required()
            }),
            defineField({
              name: 'sq',
              title: 'Albanian Hint',
              type: 'string',
              initialValue: 'Shikoni mundësitë tona më të fundit të zhvillimit',
              validation: Rule => Rule.required()
            })
          ]
        })
      ]
    })
  ],
  preview: {
    select: {
      title: 'title',
      enabled: 'enabled',
      projectCount: 'projects.length'
    },
    prepare(selection) {
      const { title, enabled, projectCount = 0 } = selection;
      return {
        title: title || 'Welcome Popup',
        subtitle: `${enabled ? 'Active' : 'Inactive'} • ${projectCount} project${projectCount === 1 ? '' : 's'}`
      };
    }
  }
})

// schemas/index.ts
import welcomePopup from './welcomePopup'

export const schemaTypes = [welcomePopup]

// Types file for TypeScript users
// types/welcomePopup.ts
export interface LocalizedField<T> {
  en: T;
  sq: T;
}

export interface PopupProject {
  name: LocalizedField<string>;
  subtitle: LocalizedField<string>;
  date: string;
  path: LocalizedField<string>; // Changed from string to LocalizedField
  mediaType: 'image' | 'video';
  image?: LocalizedField<{
    asset: {
      _ref: string;
      _type: 'reference';
    }
  }>;
  video?: LocalizedField<{
    asset: {
      _ref: string;
      _type: 'reference';
    }
  }>;
  videoUrl?: LocalizedField<string>;
  videoThumbnail?: LocalizedField<{
    asset: {
      _ref: string;
      _type: 'reference';
    }
  }>;
}

export interface UIText {
  buttonText: LocalizedField<string>;
  switchText: LocalizedField<string>;
  hintText: LocalizedField<string>;
}

export interface WelcomePopup {
  _id: string;
  _type: 'welcomePopup';
  title: string;
  enabled: boolean;
  displayFrequency: number;
  displayDelay: number;
  projects: PopupProject[];
  uiText: UIText;
}