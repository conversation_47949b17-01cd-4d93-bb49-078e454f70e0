/* General styles */
body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f4f4f4;
  }
  
  .projects-details-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
    display: flex;
    gap: 40px;
  }
  
  /* Main content area */
  .projects-details-content {
    flex: 2;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 30px;
    overflow-x: hidden;
  }
  
  .projects-title {
    font-size: 28px;
    color: #333;
    margin-bottom: 20px;
    border-bottom: 2px solid #e41e26;
    padding-bottom: 10px;
  }
  
  .projects-image {
    width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 20px;
  }
  
  .projects-body {
    font-size: 16px;
    line-height: 1.6;
    color: #444;
  }
  
  .projects-body img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 10px 0;
  }
  
  /* PDF Section */
  .pdf-file {
    background-color: #f9f9f9;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    margin-top: 30px;
  }
  
  .pdf-file h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 18px;
  }
  
  .pdf-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .pdf-button {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    font-size: 14px;
    transition: background-color 0.3s, color 0.3s;
  }
  
  .pdf-button svg {
    margin-right: 8px;
  }
  
  .pdf-button.download {
    background-color: #e41e26;
    color: #fff;
  }
  
  .pdf-button.download:hover {
    background-color: #b81821;
  }
  
  .pdf-button.view {
    background-color: #f0f0f0;
    color: #333;
  }
  
  .pdf-button.view:hover {
    background-color: #e0e0e0;
  }
  
  /* Tags */
  .projects-tags {
    margin-top: 30px;
  }
  
  .tag {
    display: inline-block;
    background-color: #e41e26;
    color: #fff;
    padding: 5px 10px;
    margin-right: 10px;
    margin-bottom: 10px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 14px;
    transition: background-color 0.3s;
  }
  
  .tag:hover {
    background-color: #b81821;
  }
  
  /* Gallery */
  .gallery-section {
    margin-top: 30px;
    margin-bottom: 30px;
  }
  
  .gallery-section h3 {
    font-size: 20px;
    color: #333;
    margin-bottom: 15px;
  }
  
  .gallery-slider {
    position: relative;
    width: 100%;
    height: 300px;
    background-color: #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
  }
  
  .gallery-image-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .gallery-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
  
  .gallery-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(228, 30, 38, 0.7);
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background 0.3s;
    z-index: 10;
  }
  
  .gallery-arrow:hover {
    background: rgba(228, 30, 38, 0.9);
  }
  
  .left-arrow {
    left: 10px;
  }
  
  .right-arrow {
    right: 10px;
  }
  
  .gallery-caption {
    text-align: center;
    margin-top: 10px;
    color: #666;
    font-style: italic;
  }
  
  /* Navigation */
  .projects-navigation {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
  }
  
  .projects-nav-link {
    display: flex;
    align-items: center;
    color: #e41e26;
    font-size: 14px;
    text-decoration: none;
    transition: color 0.3s;
  }
  
  .projects-nav-link:hover {
    color: #b81821;
  }
  
  .projects-nav-link svg {
    transition: transform 0.3s ease;
  }
  
  .prev svg {
    margin-right: 10px;
  }
  
  .next svg {
    margin-left: 10px;
  }
  
  .projects-nav-link:hover svg {
    transform: translateX(-5px);
  }
  
  .next:hover svg {
    transform: translateX(5px);
  }
  
  /* Sidebar */
  .projects-sidebar {
    flex: 1;
    position: sticky;
    top: 20px;
    align-self: flex-start;
  }
  
  .latest-news {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 20px;
  }
  
  .latest-news h2 {
    font-size: 22px;
    color: #333;
    margin-bottom: 20px;
    border-bottom: 2px solid #e41e26;
    padding-bottom: 10px;
  }
  
  .news-item {
    margin-bottom: 20px;
  }
  
  .news-image {
    width: 100%;
    height: 180px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 15px;
  }
  
  .news-item h3 {
    font-size: 16px;
    color: #333;
    margin-bottom: 10px;
  }
  
  .read-more {
    display: inline-block;
    padding: 8px 15px;
    background-color: #e41e26;
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
    transition: background-color 0.3s;
  }
  

  .news-navigation {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
  }
  
  .news-nav-button {
    background: none;
    border: none;
    cursor: pointer;
    color: #e41e26;
    transition: color 0.3s;
  }
  
  .news-nav-button:hover {
    color: #b81821;
  }
  
  /* Responsive styles */
  @media (max-width: 768px) {
    .projects-details-container {
      flex-direction: column;
    }
  
    .projects-sidebar {
      position: static;
      margin-top: 40px;
    }
  
    .pdf-buttons {
      flex-direction: column;
    }
  
    .gallery-slider {
      height: 250px;
    }
  
    .projects-navigation {
      flex-direction: column;
      gap: 20px;
    }
  
    .projects-nav-link {
      width: 100%;
      justify-content: center;
    }
  }
  
  @media (max-width: 480px) {
    .projects-title {
      font-size: 24px;
    }
  
    .projects-body {
      font-size: 14px;
    }
  
    .gallery-slider {
      height: 200px;
    }
  
    .pdf-file h3 {
      font-size: 16px;
    }
  
    .pdf-button {
      font-size: 12px;
    }
  
    .latest-news h2 {
      font-size: 20px;
    }
  
    .news-item h3 {
      font-size: 14px;
    }
  }