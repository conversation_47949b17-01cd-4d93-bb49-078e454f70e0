import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useParams } from 'react-router-dom';
import sanityClient from '../../sanity';
import { useLanguage } from '../../components/languageUtils';
import SEO from '../../components/SEO';
import { seoTitles } from '../../config/seoTitles';

const StrategicProjects = () => {
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { language, translations } = useLanguage();
  const { lang } = useParams();
  const seoData = seoTitles.strategicProjects[language];

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const query = `*[
          _type == "projectsPost" && 
          language == $language && 
          isProject == true &&
          "strategic-projects" in projectCategories
        ] | order(projectDate desc) {
          title,
          "slug": slug.current,
          mainImage {
            asset-> {
              _id,
              url
            },
            alt
          },
          description,
          projectDate
        }`;

        const result = await sanityClient.fetch(query, { language: lang });
        setProjects(result);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching projects:', err);
        setError("Failed to load projects.");
        setLoading(false);
      }
    };

    fetchProjects();
  }, [lang]);

  if (loading) return <div className="text-center py-10">{translations?.common?.loading || 'Loading...'}</div>;
  if (error) return <div className="text-center py-10 text-red-600">{error}</div>;

  const getLocalizedProjectsPrefix = (language) => {
    return language === 'en' ? 'projects' : 'projekte';
  };

  return (
    <>
      <SEO
        customTitle={seoData.title}
        customDescription={seoData.description}
        language={language}
        ogType="website"
      />
      <div className="container mx-auto px-4 py-12">
      <div className="bg-white border-b border-gray-200 mb-10">
        <div className="max-w-3xl mx-auto text-center py-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {language === 'en' ? 'Strategic Projects' : 'Projekte investimi strategjike'}
          </h1>
          <div className="w-24 h-1 bg-red-600 mx-auto mb-6"></div>
          <p className="text-gray-700 max-w-3xl mx-auto">
            {language === 'en' 
              ? 'Our strategic projects are cornerstone initiatives designed to drive significant economic growth and create long-term value for Albania.'
              : 'Projektet tona strategjike janë nisma thelbësore të dizajnuara për të nxitur rritje të rëndësishme ekonomike dhe për të krijuar vlerë afatgjatë për Shqipërinë.'}
          </p>
        </div>
      </div>

      {projects.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project) => {
            const projectsPrefix = getLocalizedProjectsPrefix(language);
            const permalink = `/${language}/${projectsPrefix}/${project.slug}`;
            const projectDate = project.projectDate ? new Date(project.projectDate).toLocaleDateString(language === 'en' ? 'en-US' : 'sq-AL', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            }) : null;

            return (
              <div key={project.slug} className="group bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-all duration-300">
                <div className="h-52 overflow-hidden">
                  {project.mainImage?.asset?.url ? (
                    <img 
                      src={project.mainImage.asset.url} 
                      alt={project.mainImage.alt || project.title}
                      className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = 'https://via.placeholder.com/400x250?text=No+Image';
                      }}
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                      <span className="text-gray-500">No Image Available</span>
                    </div>
                  )}
                  
                  {/* Pin marker in top-right corner */}
                  <div className="absolute top-4 right-4 w-8 h-8 bg-red-600 rounded-full flex items-center justify-center shadow-lg transform group-hover:scale-110 transition-transform duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                <div className="p-6">
                  {projectDate && (
                    <p className="text-sm text-red-600 mb-2">{projectDate}</p>
                  )}
                  <h3 className="text-xl font-semibold mb-3">
                    <Link to={permalink} className="text-gray-800 hover:text-red-600 transition duration-300">
                      {project.title}
                    </Link>
                  </h3>
                  {project.description && (
                    <p className="text-gray-600 line-clamp-3 mb-4">{project.description}</p>
                  )}
                  <Link 
                    to={permalink} 
                    className="inline-block mt-4 py-2 px-4 bg-gray-100 text-gray-800 hover:bg-red-600 hover:text-white rounded-md transition-colors duration-300 font-medium"
                  >
                    {language === 'en' ? 'View Project' : 'Shiko Projektin'}
                  </Link>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="bg-white rounded-lg p-8 text-center border border-gray-200">
          <h3 className="text-xl font-semibold text-gray-800 mb-3">
            {language === 'en' ? 'No strategic projects available yet' : 'Nuk ka ende projekte strategjike të disponueshme'}
          </h3>
          <p className="text-gray-600 max-w-2xl mx-auto">
            {language === 'en' 
              ? 'We are currently developing new strategic projects. Please check back soon for updates on our latest initiatives.'
              : 'Aktualisht po zhvillojmë projekte të reja strategjike. Ju lutemi kontrolloni sërish së shpejti për përditësime mbi iniciativat tona më të fundit.'}
          </p>
        </div>
      )}
    </div>
    </>
  );
};

export default StrategicProjects;