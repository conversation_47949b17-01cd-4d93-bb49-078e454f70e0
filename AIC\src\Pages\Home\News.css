/* Base Styles (Desktop) */
.aic-news-section {
  background-color: #f3f4f6;
  padding: 2rem;
  background-size: cover;
  background-position: center;
  position: relative;
}

.aic-news-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
}

.aic-news-content {
  position: relative;
  z-index: 1;
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 2rem;
}

.aic-news-empty-message {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
  text-align: center;
  font-size: 1.25rem;
  color: #e41e26;
  background-color: rgba(243, 244, 246, 0.8);
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  margin: 20px auto;
  width: 100%;
  max-width: 1200px;
}

.aic-news-header {
  text-align: center;
  margin-bottom: 2rem;
}

.aic-news-subheading {
  color: #ffffff;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.aic-news-heading {
  color: #ffffff;
  font-size: 2.25rem;
  font-weight: bold;
}

.aic-news-heading-underline {
  width: 4rem;
  height: 0.25rem;
  background-color: #e41e26;
  margin: 0.5rem auto;
  margin-top: 20px;
}

.aic-news-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 3rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Card Styles */
.aic-news-card {
  display: flex;
  background-color: white;
  border-radius: 2rem;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 100%;
}

.aic-news-card-image-container {
  flex: 0 0 50%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.aic-news-card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  position: absolute;
  top: 0;
  left: 0;
}

.aic-news-card-content {
  flex: 0 0 50%;
  padding: 1.75rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.aic-news-category {
  color: #e41e26;
  font-size: 1.125rem;
  text-align: left;
  margin-bottom: 0.5rem;
}

.aic-news-card-title {
  font-weight: bold;
  font-size: 1.375rem;
  text-align: left;
  margin-bottom: 0.75rem;
}

.aic-news-card-description {
  color: #4b5563;
  font-size: 1rem;
  text-align: left;
  flex-grow: 1;
  margin-bottom: 1rem;
}

.aic-news-read-more {
  color: #e41e26;
  font-size: 1.125rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  text-align: left;
  text-decoration: none;
  align-self: flex-start;
}

.aic-news-see-more-button {
  display: block;
  margin: 3rem auto 0;
  padding: 0.75rem 1.5rem;
  background-color: #e41e26;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 1.125rem;
  cursor: pointer;
  text-decoration: none;
  text-align: center;
  max-width: 200px;
}

.aic-news-loading,
.aic-news-error {
  text-align: center;
  font-size: 1.25rem;
  color: #ffffff;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 1rem;
  border-radius: 0.5rem;
  margin: 2rem 0;
}

/* Tablet Styles */
@media (max-width: 1200px) {
  .aic-news-grid {
    grid-template-columns: 1fr;
    gap: 2.5rem;
    max-width: 900px;
  }

  .aic-news-card {
    flex-direction: row;
  }

  .aic-news-card-image-container {
    height: 280px;
  }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .aic-news-section {
    padding: 1rem;
  }

  .aic-news-content {
    padding: 0 1rem;
  }

  .aic-news-heading {
    font-size: 1.75rem;
  }

  .aic-news-heading-underline {
    width: 3rem;
    margin-top: 15px;
  }

  .aic-news-grid {
    gap: 2rem;
  }

  .aic-news-card {
    flex-direction: column;
  }

  .aic-news-card-image-container {
    width: 100%;
    height: 200px;
  }

  .aic-news-card-content {
    width: 100%;
    padding: 1.5rem;
  }

  .aic-news-category {
    font-size: 1rem;
  }

  .aic-news-card-title {
    font-size: 1.25rem;
  }

  .aic-news-card-description {
    font-size: 0.9375rem;
  }

  .aic-news-read-more {
    font-size: 1rem;
    margin-top: 1rem;
  }
}