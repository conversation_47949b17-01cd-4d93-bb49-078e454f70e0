import React, { useState, useEffect, useRef } from 'react';
import 'leaflet/dist/leaflet.css';
import ReactDOM from 'react-dom';
import L from 'leaflet';
import sanityClient from '../../sanity'; // Update path to match your project structure
import imageUrlBuilder from '@sanity/image-url';

// Initialize image URL builder
const builder = imageUrlBuilder(sanityClient);
function urlFor(source) {
  return builder.image(source);
}

// Global variable to track map instance across renders
let globalMapInstance = null;

function Map() {
  const [map, setMap] = useState(null);
  const [properties, setProperties] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedProperty, setSelectedProperty] = useState(null);
  const [showPhotoModal, setShowPhotoModal] = useState(false);
  const [photoUrl, setPhotoUrl] = useState('');
  const [photoTitle, setPhotoTitle] = useState('');
  const [photoError, setPhotoError] = useState(false);
  const [loadingPhoto, setLoadingPhoto] = useState(false);
  const [showLeftSidebar, setShowLeftSidebar] = useState(true);
  const [showRightSidebar, setShowRightSidebar] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [mapError, setMapError] = useState(null);
  const [currentLanguage, setCurrentLanguage] = useState('en'); // Default to English
  const mapContainerRef = useRef(null);
  const drawnItemsRef = useRef(null);
  const mapInitializedRef = useRef(false);
  const originalOverflowRef = useRef('');
  const resizeTimeoutRef = useRef(null);

  // Debug logging function
  const debugLog = (message, data) => {
    console.log(`[MAP DEBUG] ${message}`, data || '');
  };

  // Fetch properties from Sanity - updated to match the new schema with localized area fields
  useEffect(() => {
    setLoading(true);
    sanityClient
      .fetch(
        `*[_type == "property"]{
          _id,
          title,
          geopoint,
          location,
          distanceCityCenter,
          distanceTIA,
          function,
          status,
          value,
          partnerships,
          developer,
          manager,
          beneficiary,
          designer,
          areaSize{en, sq},
          constructionArea{en, sq},
          businessSpace{en, sq},
          hotelSpace{en, sq},
          officeSpace{en, sq},
          residentialSpace{en, sq},
          projectImage{
            en{asset->{_id, url}},
            sq{asset->{_id, url}}
          },
          mapImage{
            en{asset->{_id, url}},
            sq{asset->{_id, url}}
          },
          developmentPermit{
            en{asset->{_id, url}},
            sq{asset->{_id, url}}
          },
          constructionPermit{
            en{asset->{_id, url}},
            sq{asset->{_id, url}}
          },
          projectPDF{
            en{asset->{_id, url}},
            sq{asset->{_id, url}}
          },
          gallery[]{asset->{_id, url}},
          description
        }`
      )
      .then((data) => {
        console.log("Fetched property data:", data);
        
        // Transform Sanity data to match the format expected by the map
        const transformedData = data.map(item => {
          // Extract coordinates from geopoint
          const coordinates = item.geopoint 
            ? [item.geopoint.lat, item.geopoint.lng] 
            : [41.3275, 19.8189]; // Default to Albania center if no coordinates
          
          return {
            id: item._id,
            titulli: item.title?.[currentLanguage] || item.title?.en || item.title?.sq || 'Untitled',
            vendndodhja: coordinates,
            location: item.location?.[currentLanguage] || item.location?.en || item.location?.sq || '',
            distanca_qendra: item.distanceCityCenter?.[currentLanguage] || item.distanceCityCenter?.en || item.distanceCityCenter?.sq || '',
            distanca_TIA: item.distanceTIA?.[currentLanguage] || item.distanceTIA?.en || item.distanceTIA?.sq || '',
            funksioni: item.function?.[currentLanguage] || item.function?.en || item.function?.sq || '',
            statusi: item.status?.[currentLanguage] || item.status?.en || item.status?.sq || '',
            vlera: item.value?.[currentLanguage] || item.value?.en || item.value?.sq || '',
            bashkepunime: item.partnerships?.[currentLanguage] || item.partnerships?.en || item.partnerships?.sq || '',
            zhvilluesi: item.developer?.[currentLanguage] || item.developer?.en || item.developer?.sq || '',
            menaxheri: item.manager?.[currentLanguage] || item.manager?.en || item.manager?.sq || '',
            perfituesi: item.beneficiary?.[currentLanguage] || item.beneficiary?.en || item.beneficiary?.sq || '',
            projektuesi: item.designer?.[currentLanguage] || item.designer?.en || item.designer?.sq || '',
            // Updated area fields to use the localized values
            siperfaqe_zone: item.areaSize?.[currentLanguage] || item.areaSize?.en || item.areaSize?.sq || "TBD",
            siperfaqe_nder: item.constructionArea?.[currentLanguage] || item.constructionArea?.en || item.constructionArea?.sq || "TBD",
            biznes: item.businessSpace?.[currentLanguage] || item.businessSpace?.en || item.businessSpace?.sq || "TBD",
            hoteleri: item.hotelSpace?.[currentLanguage] || item.hotelSpace?.en || item.hotelSpace?.sq || "TBD",
            zyra: item.officeSpace?.[currentLanguage] || item.officeSpace?.en || item.officeSpace?.sq || "TBD",
            rezidence: item.residentialSpace?.[currentLanguage] || item.residentialSpace?.en || item.residentialSpace?.sq || "TBD",
            foto_projekti: item.projectImage?.[currentLanguage]?.asset?.url || 
                            item.projectImage?.en?.asset?.url || 
                            item.projectImage?.sq?.asset?.url || '',
            foto_harte: item.mapImage?.[currentLanguage]?.asset?.url || 
                         item.mapImage?.en?.asset?.url || 
                         item.mapImage?.sq?.asset?.url || '',
            foto_leje_zhvillimi: item.developmentPermit?.[currentLanguage]?.asset?.url || 
                                  item.developmentPermit?.en?.asset?.url || 
                                  item.developmentPermit?.sq?.asset?.url || '',
            foto_leje_ndertimi: item.constructionPermit?.[currentLanguage]?.asset?.url || 
                                 item.constructionPermit?.en?.asset?.url || 
                                 item.constructionPermit?.sq?.asset?.url || '',
            pdf_projekti: item.projectPDF?.[currentLanguage]?.asset?.url || 
                           item.projectPDF?.en?.asset?.url || 
                           item.projectPDF?.sq?.asset?.url || '',
            gallery: item.gallery || [],
            description: item.description?.[currentLanguage] || item.description?.en || item.description?.sq || []
          };
        });
        
        setProperties(transformedData);
        setLoading(false);
      })
      .catch((err) => {
        console.error("Error fetching property data:", err);
        setError(err);
        setLoading(false);
      });
  }, [currentLanguage]);

  // Fix Leaflet's default icon paths
  useEffect(() => {
    delete L.Icon.Default.prototype._getIconUrl;
    L.Icon.Default.mergeOptions({
      iconRetinaUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
      iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
      shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
    });
  }, []);

  // Check if device is mobile on component mount and window resize
  useEffect(() => {
    const checkIfMobile = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      
      // Auto-hide sidebars on small screens
      if (mobile) {
        setShowLeftSidebar(false);
        setShowRightSidebar(false);
      } else {
        setShowLeftSidebar(true);
        setShowRightSidebar(!!selectedProperty);
      }
    };
    
    checkIfMobile();
    
    // Debounce the resize event to prevent multiple rapid calls
    const handleResize = () => {
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }
      
      resizeTimeoutRef.current = setTimeout(() => {
        checkIfMobile();
      }, 300);
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }
    };
  }, [selectedProperty]);

  // Update right sidebar visibility when a property is selected
  useEffect(() => {
    if (selectedProperty && !isMobile) {
      setShowRightSidebar(true);
    }
  }, [selectedProperty, isMobile]);

  // Prevent body scrolling when modal is open
  useEffect(() => {
    if (showPhotoModal) {
      originalOverflowRef.current = document.body.style.overflow;
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = originalOverflowRef.current;
    }
    
    return () => {
      document.body.style.overflow = originalOverflowRef.current;
    };
  }, [showPhotoModal]);

  // Create modal container
  useEffect(() => {
    if (!document.getElementById('photo-modal-container')) {
      const modalContainer = document.createElement('div');
      modalContainer.id = 'photo-modal-container';
      modalContainer.className = 'fixed inset-0 z-[20000] pointer-events-none';
      document.body.appendChild(modalContainer);

      // Add responsive styles
      const style = document.createElement('style');
      style.textContent = `
        #photo-modal-container.active {
          pointer-events: auto;
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        .custom-map-marker {
          background: none;
          border: none;
        }
        .leaflet-popup-content {
          max-width: 250px;
          max-height: 300px;
          overflow-y: auto;
          overflow-x: hidden;
        }
        .leaflet-popup-content img {
          max-width: 100%;
          max-height: 200px;
          display: block;
          margin: 10px 0;
        }
        .leaflet-control-attribution {
          display: none !important;
        }
        .leaflet-control-zoom-in,
        .leaflet-control-zoom-out {
          border-radius: 4px !important;
          margin-bottom: 5px !important;
          width: 30px !important;
          height: 30px !important;
          line-height: 30px !important;
          font-size: 18px !important;
        }
        .leaflet-draw-actions {
          display: none !important;
        }
        .leaflet-control:not(.leaflet-control-zoom) {
          display: none !important;
        }
        .leaflet-control-zoom {
          border: none !important;
          box-shadow: 0 1px 5px rgba(0,0,0,0.2) !important;
        }
        .leaflet-bar a {
          background-color: white !important;
        }
        .leaflet-container {
          width: 100% !important;
          height: 100% !important;
          z-index: 1;
          touch-action: manipulation;
        }
        @media (max-width: 768px) {
          .leaflet-popup-content {
            max-width: 200px;
          }
          .leaflet-control-zoom {
            right: 10px !important;
            left: auto !important;
            bottom: 70px !important;
            top: auto !important;
          }
        }
      `;
      document.head.appendChild(style);
    }
  }, []);
    
  // State to track the current map tile style
  const [mapStyle, setMapStyle] = useState('default'); // Options: default, satellite
  
  // Initialize the map - this is now the ONLY place where map is created or destroyed
  useEffect(() => {
    // Only initialize map once properties are loaded
    if (loading || properties.length === 0) return;

    const initializeMap = async () => {
      try {
        // CRITICAL FIX: Always clean up any existing map instance first
        if (globalMapInstance) {
          debugLog("Cleaning up existing map instance");
          globalMapInstance.remove();
          globalMapInstance = null;
        }
        
        // Reset initialization flag
        mapInitializedRef.current = false;
        
        // Clear the map container completely
        const mapContainer = document.getElementById('map');
        if (mapContainer) {
          mapContainer.innerHTML = '';
        }
        
        // Make sure container exists and has dimensions
        if (!mapContainer) {
          setMapError("Map container not found");
          return;
        }
        
        debugLog("Map container dimensions", {
          width: mapContainer.clientWidth,
          height: mapContainer.clientHeight
        });
        
        if (mapContainer.clientWidth === 0 || mapContainer.clientHeight === 0) {
          debugLog("Map container has zero dimensions, waiting...");
          // Add a small delay and retry if dimensions are zero
          return;
        }
        
        // Create a new map instance - default to Albania center
        debugLog("Creating new map instance");
        const mapInstance = L.map('map', {
          zoomControl: false,
          attributionControl: false
        }).setView([41.3275, 19.8189], 8);
        
        // CRITICAL FIX: Store map instance in global variable
        globalMapInstance = mapInstance;
        
        // Add zoom control
        L.control.zoom({
          position: 'topright'
        }).addTo(mapInstance);
        
        // Available map styles
        const mapStyles = {
          default: {
            url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
            attribution: '&copy; OpenStreetMap contributors'
          },
          satellite: {
            url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
            attribution: 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community'
          }
        };
        
        // Add tile layer based on selected style
        const selectedStyle = mapStyles[mapStyle] || mapStyles.default;
        L.tileLayer(selectedStyle.url, {
          attribution: selectedStyle.attribution
        }).addTo(mapInstance);
        
        // Create custom marker icon
        const googleMapsSVG = `
          <svg xmlns="http://www.w3.org/2000/svg" width="32" height="44" viewBox="0 0 32 44">
            <path fill="#E53935" d="M16,0C7.16,0,0,7.16,0,16c0,9.5,16,28,16,28s16-18.5,16-28C32,7.16,24.84,0,16,0z"/>
            <circle fill="#FFFFFF" cx="16" cy="16" r="6"/>
          </svg>
        `;
        
        const customIcon = L.divIcon({
          className: 'custom-map-marker',
          html: googleMapsSVG,
          iconSize: [32, 44],
          iconAnchor: [16, 44],
          popupAnchor: [0, -44]
        });
        
        // Add markers for each property
        properties.forEach((prone) => {
          // Skip if coordinates are invalid
          if (!prone.vendndodhja || prone.vendndodhja.length !== 2) {
            console.warn(`Property "${prone.titulli}" has invalid coordinates`);
            return;
          }
          
          const marker = L.marker(prone.vendndodhja, {
            icon: customIcon
          }).addTo(mapInstance);
          
          // Popup content
          const popupContent = `
          <strong class="block text-lg font-medium text-center border-b border-gray-200 pb-2 mb-2">${prone.titulli}</strong>
          <div class="mt-2 text-sm">
            <div class="flex items-center justify-between py-1">
              <span>Project Photo:</span>
              <button class="view-photo px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-xs" 
                data-photo="${prone.foto_projekti}" 
                data-title="Project Photo - ${prone.titulli}">
                View
              </button>
            </div>
            <div class="flex items-center justify-between py-1">
              <span>Map Photo:</span>
              <button class="view-photo px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-xs" 
                data-photo="${prone.foto_harte}" 
                data-title="Map Photo - ${prone.titulli}">
                View
              </button>
            </div>
          </div>
          `;
          
          marker.bindPopup(popupContent);
          
          marker.on('click', () => {
            setSelectedProperty(prone);
            // Auto-open details panel on mobile
            if (isMobile) {
              setShowRightSidebar(true);
              setShowLeftSidebar(false);
            }
          });
        });
        
        // Event listener for photo view links in popups
        mapInstance.on('popupopen', function(e) {
          const popup = e.popup;
          const container = popup.getElement();
          
          const photoLinks = container.querySelectorAll('.view-photo');
          photoLinks.forEach(link => {
            link.addEventListener('click', function(e) {
              e.preventDefault();
              e.stopPropagation();
              const photoUrl = this.getAttribute('data-photo');
              const photoTitle = this.getAttribute('data-title') || '';
              openPhotoModal(photoUrl, photoTitle);
            });
          });
        });
        
        // Setup draw control if Leaflet-Draw is available
        try {
          if (window.L.Draw) {
            const drawnItems = new L.FeatureGroup();
            mapInstance.addLayer(drawnItems);
            drawnItemsRef.current = drawnItems;
            
            const drawControl = new L.Control.Draw({
              draw: {
                polygon: true,
                polyline: false,
                rectangle: true,
                circle: false,
                marker: false,
                circlemarker: false
              },
              edit: {
                featureGroup: drawnItems
              }
            });
            mapInstance.addControl(drawControl);
            
            mapInstance.on(L.Draw.Event.CREATED, function (event) {
              const layer = event.layer;
              drawnItems.addLayer(layer);
              
              const data = drawnItems.toGeoJSON();
              localStorage.setItem("savedShapes", JSON.stringify(data));
              alert("Polygon saved successfully!");
            });
            
            const saved = localStorage.getItem("savedShapes");
            if (saved) {
              const geojson = JSON.parse(saved);
              L.geoJSON(geojson).eachLayer(function (layer) {
                drawnItems.addLayer(layer);
              });
            }
          }
        } catch (err) {
          console.log('Leaflet-draw not available, skipping drawing functionality');
        }
        
        // Update state with the new map instance
        setMap(mapInstance);
        mapInitializedRef.current = true;
        setMapError(null);
        
        // Force a resize after a delay to handle any container sizing issues
        setTimeout(() => {
          if (mapInstance && !mapInstance._leaflet_id) {
            // Map instance was destroyed, don't try to resize
            return;
          }
          try {
            mapInstance.invalidateSize();
          } catch (e) {
            // Silently ignore errors during resize
            debugLog("Error during map resize", e);
          }
        }, 500);
      } catch (error) {
        console.error("Error initializing map:", error);
        setMapError(`Map initialization failed: ${error.message}`);
      }
    };
    
    // Initialize the map with a delay
    const timer = setTimeout(() => {
      initializeMap();
    }, 300);
    
    // Cleanup function
    return () => {
      clearTimeout(timer);
      debugLog("Map component unmounting");
      
      // CRITICAL FIX: Clean up map instance on component unmount
      if (globalMapInstance) {
        try {
          debugLog("Removing map instance during cleanup");
          globalMapInstance.remove();
          globalMapInstance = null;
        } catch (err) {
          debugLog("Error removing map during cleanup", err);
        }
      }
    };
  }, [loading, properties, isMobile]);

  // Handle responsive transitions without recreating the map
  useEffect(() => {
    // Only run this effect if the map exists
    if (!map || !globalMapInstance) return;
    
    debugLog("Responsive change detected, resizing map");
    
    // Delay resize to let DOM update first
    const timer = setTimeout(() => {
      if (globalMapInstance && globalMapInstance._leaflet_id) {
        try {
          globalMapInstance.invalidateSize({ animate: false });
          debugLog("Map resized after responsive change");
        } catch (e) {
          debugLog("Error resizing map after responsive change", e);
        }
      }
    }, 300);
    
    return () => {
      clearTimeout(timer);
    };
  }, [isMobile, showLeftSidebar, showRightSidebar, map]);
  
  // Update map tiles when style changes
  useEffect(() => {
    if (!map || !globalMapInstance) return;
    
    debugLog(`Changing map style to: ${mapStyle}`);
    
    // Available map styles
    const mapStyles = {
      default: {
        url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
        attribution: '&copy; OpenStreetMap contributors'
      },
      satellite: {
        url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
        attribution: 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community'
      }
    };
    
    // Find all existing tile layers and remove them
    globalMapInstance.eachLayer(layer => {
      if (layer instanceof L.TileLayer) {
        globalMapInstance.removeLayer(layer);
      }
    });
    
    // Add the new tile layer
    const selectedStyle = mapStyles[mapStyle] || mapStyles.default;
    L.tileLayer(selectedStyle.url, {
      attribution: selectedStyle.attribution
    }).addTo(globalMapInstance);
    
  }, [mapStyle, map]);

  // Function to manually reload the map if needed
  const reloadMap = () => {
    debugLog("Manual map reload triggered");
    
    // Clean up existing map
    if (globalMapInstance) {
      try {
        globalMapInstance.remove();
      } catch (err) {
        debugLog("Error removing map during reload", err);
      }
      globalMapInstance = null;
    }
    
    setMap(null);
    mapInitializedRef.current = false;
    
    // Clear the map container
    const mapContainer = document.getElementById('map');
    if (mapContainer) {
      mapContainer.innerHTML = '';
    }
    
    // Re-initialize map (will trigger through the useEffect)
    setMapError("Reloading map...");
    setTimeout(() => setMapError(null), 300);
  };

  // Update selected property when language changes
  useEffect(() => {
    // Only run this if there's a selected property and properties have been loaded
    if (selectedProperty && properties.length > 0) {
      // Find the currently selected property in the updated properties array (with new language data)
      const updatedProperty = properties.find(prop => prop.id === selectedProperty.id);
      
      if (updatedProperty) {
        // Update the selected property with the new language data
        setSelectedProperty(updatedProperty);
        
        // Update the popup content if it's open
        if (globalMapInstance) {
          globalMapInstance.eachLayer(layer => {
            // Check if this is a marker and has a popup
            if (layer instanceof L.Marker && layer.getPopup) {
              const popup = layer.getPopup();
              if (popup && popup.isOpen()) {
                // If the popup for this property is open, update its content
                if (layer._latlng.lat === updatedProperty.vendndodhja[0] && 
                    layer._latlng.lng === updatedProperty.vendndodhja[1]) {
                  
                  const popupContent = `
                  <strong class="block text-lg font-medium text-center border-b border-gray-200 pb-2 mb-2">${updatedProperty.titulli}</strong>
                  <div class="mt-2 text-sm">
                    <div class="flex items-center justify-between py-1">
                      <span>${currentLanguage === 'en' ? 'Project Photo:' : 'Foto e Projektit:'}</span>
                      <button class="view-photo px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-xs" 
                        data-photo="${updatedProperty.foto_projekti}" 
                        data-title="${currentLanguage === 'en' ? 'Project Photo' : 'Foto e Projektit'} - ${updatedProperty.titulli}">
                        ${currentLanguage === 'en' ? 'View' : 'Shiko'}
                      </button>
                    </div>
                    <div class="flex items-center justify-between py-1">
                      <span>${currentLanguage === 'en' ? 'Map Photo:' : 'Foto e Hartës:'}</span>
                      <button class="view-photo px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-xs" 
                        data-photo="${updatedProperty.foto_harte}" 
                        data-title="${currentLanguage === 'en' ? 'Map Photo' : 'Foto e Hartës'} - ${updatedProperty.titulli}">
                        ${currentLanguage === 'en' ? 'View' : 'Shiko'}
                      </button>
                    </div>
                  </div>
                  `;
                  
                  popup.setContent(popupContent);
                  
                  // Rebind the click events for the new buttons
                  setTimeout(() => {
                    if (popup.getElement()) {
                      const container = popup.getElement();
                      const photoLinks = container.querySelectorAll('.view-photo');
                      photoLinks.forEach(link => {
                        link.addEventListener('click', function(e) {
                          e.preventDefault();
                          e.stopPropagation();
                          const photoUrl = this.getAttribute('data-photo');
                          const photoTitle = this.getAttribute('data-title') || '';
                          openPhotoModal(photoUrl, photoTitle);
                        });
                      });
                    }
                  }, 0);
                }
              }
            }
          });
        }
      }
    }
  }, [currentLanguage, properties]);

  // Function to show property details
  const showPropertyDetails = (prone) => {
    setSelectedProperty(prone);
    if (globalMapInstance) {
      try {
        globalMapInstance.setView(prone.vendndodhja, 17);
      } catch (err) {
        debugLog("Error setting map view", err);
      }
    }
    
    // Auto-hide project list on mobile
    if (isMobile) {
      setShowLeftSidebar(false);
      setShowRightSidebar(true);
    }
  };

  // Function to toggle sidebars
  const toggleLeftSidebar = () => {
    setShowLeftSidebar(!showLeftSidebar);
    if (isMobile && !showLeftSidebar) {
      setShowRightSidebar(false);
    }
  };

  const toggleRightSidebar = () => {
    setShowRightSidebar(!showRightSidebar);
    if (isMobile && !showRightSidebar) {
      setShowLeftSidebar(false);
    }
  };

  // Photo modal functions
  const openPhotoModal = (url, title) => {
    setLoadingPhoto(true);
    setPhotoError(false);
    setPhotoUrl(url);
    setPhotoTitle(title);
    setShowPhotoModal(true);
    
    const modalContainer = document.getElementById('photo-modal-container');
    if (modalContainer) {
      modalContainer.classList.add('active');
      modalContainer.style.pointerEvents = 'auto';
    }
  };

  const closePhotoModal = (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    
    setShowPhotoModal(false);
    
    const modalContainer = document.getElementById('photo-modal-container');
    if (modalContainer) {
      modalContainer.classList.remove('active');
      modalContainer.style.pointerEvents = 'none';
    }
    
    setTimeout(() => {
      setPhotoUrl('');
      setPhotoTitle('');
      setLoadingPhoto(false);
      setPhotoError(false);
    }, 300);
  };

  const handleModalBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      closePhotoModal();
    }
  };
  
  // Responsive Photo Modal
  const PhotoModal = () => {
    if (!showPhotoModal) return null;
    
    return (
      <div 
        className="fixed inset-0 bg-black bg-opacity-90 backdrop-blur-sm flex items-center justify-center p-4 z-[20000]" 
        onClick={handleModalBackdropClick}
        style={{ touchAction: 'none' }}
      >
        {/* Close button - larger on mobile */}
        <button 
          type="button"
          className="fixed top-3 right-3 md:top-6 md:right-6 bg-red-500 bg-opacity-90 text-white border-2 border-white rounded-full w-10 h-10 md:w-12 md:h-12 flex items-center justify-center text-2xl md:text-3xl font-bold shadow-lg hover:bg-red-600 hover:scale-105 transition-all z-[20001]"
          onClick={closePhotoModal}
          aria-label="Close"
        >
          ×
        </button>
        
        <div className="max-w-[95%] md:max-w-[90%] max-h-[90vh] relative flex flex-col items-center z-[20001]" onClick={(e) => e.stopPropagation()}>
          {photoTitle && (
            <h3 className="text-white text-lg md:text-xl font-medium mb-3 md:mb-6 text-center max-w-full">{photoTitle}</h3>
          )}
          
          {/* Container with just the image */}
          <div className="bg-white p-2 md:p-4 rounded shadow-xl">
            {/* Main image */}
            <img 
              src={photoUrl} 
              alt={photoTitle || "Property"} 
              className="max-w-full max-h-[70vh] object-contain"
              onLoad={() => setLoadingPhoto(false)}
              onError={() => {
                console.error("Failed to load image:", photoUrl);
                setPhotoError(true);
                setLoadingPhoto(false);
              }}
            />
            
            {photoError && (
              <div className="mt-3 p-3 bg-red-50 rounded text-center">
                <p className="text-red-500 font-medium">Image failed to load</p>
                <p className="text-sm text-gray-600">Please check if the file exists at this path</p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };
  
  // Render the modal to a portal outside the React tree
  const renderModal = () => {
    const modalContainer = document.getElementById('photo-modal-container');
    if (modalContainer && showPhotoModal) {
      return ReactDOM.createPortal(<PhotoModal />, modalContainer);
    }
    return null;
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-100">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
          <p className="text-xl font-semibold text-gray-700">Loading Map Data...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-100">
        <div className="bg-white p-6 rounded-lg shadow-lg max-w-md">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Error Loading Data</h2>
          <p className="text-gray-700 mb-4">There was a problem loading the property data from Sanity:</p>
          <p className="bg-red-50 p-3 rounded text-red-700 font-mono text-sm">{error.message}</p>
          <button 
            className="mt-6 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="flex flex-col h-screen overflow-hidden">
      <header className="bg-white p-2 md:p-4 text-center border-b border-gray-200">
        <div className="grid grid-cols-3 items-center">
          {/* Left: Mobile toggle for left sidebar */}
          <div className="flex items-center justify-start">
            <button 
              className="md:hidden bg-blue-500 text-white px-2 py-1 rounded shadow"
              onClick={toggleLeftSidebar}
              aria-label="Toggle projects list"
            >
              {showLeftSidebar ? "✕" : "☰"}
            </button>
          </div>
          
          {/* Center: Logo */}
          <div className="flex justify-center">
            <img src="/logo.png" alt="Albanian Investment Corporation Logo" className="max-h-12 md:max-h-16" />
          </div>
          
          {/* Right: Language toggle + mobile right sidebar toggle */}
          <div className="flex items-center justify-end space-x-2">
            {/* Language toggle buttons */}
            <div className="flex rounded-lg overflow-hidden border border-gray-300 shadow-sm">
              <button 
                className={`px-3 py-1.5 text-sm font-medium transition-colors flex items-center ${
                  currentLanguage === 'en' 
                    ? 'bg-blue-500 text-white' 
                    : 'bg-white text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => setCurrentLanguage('en')}
              >
                <span className="mr-1.5">🇬🇧</span>
                EN
              </button>
              <button 
                className={`px-3 py-1.5 text-sm font-medium transition-colors flex items-center ${
                  currentLanguage === 'sq' 
                    ? 'bg-blue-500 text-white' 
                    : 'bg-white text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => setCurrentLanguage('sq')}
              >
                <span className="mr-1.5">🇦🇱</span>
                AL
              </button>
            </div>
            
            {/* Mobile toggle for right sidebar - only shown when there's a selected property */}
            {selectedProperty && (
              <button 
                className="md:hidden bg-blue-500 text-white px-2 py-1 rounded shadow"
                onClick={toggleRightSidebar}
                aria-label="Toggle details panel"
              >
                {showRightSidebar ? "✕" : "ℹ"}
              </button>
            )}
          </div>
        </div>
      </header>
      
      <main className="flex flex-1 overflow-hidden relative">
        {/* Property List - Collapsible on mobile */}
        <div className={`${showLeftSidebar ? 'w-full md:w-64' : 'w-0'} absolute md:relative h-full bg-gray-50 overflow-y-auto border-r border-gray-200 shadow-md transition-all duration-300 z-10`}>
          <div className="p-4">
            <h3 className="text-lg font-bold text-gray-800 mb-4 border-b pb-2">
              {currentLanguage === 'en' ? 'INVESTMENT PROJECTS' : 'PROJEKTE INVESTIMI'}
            </h3>
            <ul className="space-y-1">
              {properties.map((prone, index) => (
                <li 
                  key={prone.id || index}
                  className={`p-3 rounded-md cursor-pointer transition-colors duration-200 hover:bg-blue-50 ${
                    selectedProperty === prone ? 'bg-blue-100 font-medium' : ''
                  }`}
                  onClick={() => showPropertyDetails(prone)}
                >
                  <span className="block truncate text-gray-700">{prone.titulli}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
        
        {/* Map Container */}
        <div className="flex-1 relative">
          <div 
            id="map" 
            ref={mapContainerRef} 
            className="h-full w-full relative z-0" 
            style={{ 
              touchAction: 'manipulation',
              minHeight: '400px'
            }}
          ></div>
          
          {/* Map Style Controls - Moved to bottom left */}
          <div className="absolute bottom-6 left-4 z-20 bg-white rounded-lg shadow-md p-1.5 border border-gray-200">
            <div className="flex flex-row space-x-1">
              <button 
                onClick={() => setMapStyle('default')}
                className={`text-xs font-medium px-2.5 py-1.5 rounded ${
                  mapStyle === 'default' 
                    ? 'bg-blue-500 text-white' 
                    : 'bg-white text-gray-700 hover:bg-gray-100'
                }`}
                title={currentLanguage === 'en' ? 'Default Map' : 'Hartë Standarde'}
              >
                {currentLanguage === 'en' ? 'Default' : 'Standarde'}
              </button>
              <button 
                onClick={() => setMapStyle('satellite')}
                className={`text-xs font-medium px-2.5 py-1.5 rounded ${
                  mapStyle === 'satellite' 
                    ? 'bg-blue-500 text-white' 
                    : 'bg-white text-gray-700 hover:bg-gray-100'
                }`}
                title={currentLanguage === 'en' ? 'Satellite Map' : 'Hartë Satelitore'}
              >
                {currentLanguage === 'en' ? 'Satellite' : 'Satelitore'}
              </button>
            </div>
          </div>
          
          {/* Map loading overlay */}
          {mapError && (
            <div className="absolute inset-0 bg-white bg-opacity-80 flex flex-col items-center justify-center z-50 p-4">
              <div className="bg-red-50 border border-red-200 rounded p-4 mb-4 max-w-md">
                <h3 className="text-red-600 font-bold text-lg mb-2">Map Error</h3>
                <p className="text-red-600">{mapError}</p>
              </div>
              <button 
                onClick={reloadMap}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded shadow transition-colors"
              >
                Reload Map
              </button>
            </div>
          )}
        </div>
        
        {/* Property Details - Collapsible on mobile */}
        <div className={`${showRightSidebar ? 'w-full md:w-96' : 'w-0'} absolute top-0 right-0 md:relative h-full bg-gray-50 overflow-y-auto border-l border-gray-200 shadow-md transition-all duration-300 z-10`}>
          <div className="p-4 md:p-6">
            <h2 className="text-xl font-bold text-gray-800 mb-4 border-b pb-2 flex justify-between items-center">
              {currentLanguage === 'en' ? 'Property Details' : 'Detaje Prone'}
              {isMobile && (
                <button 
                  onClick={toggleRightSidebar}
                  className="text-gray-500 hover:text-gray-700"
                  aria-label="Close details"
                >
                  ✕
                </button>
              )}
            </h2>
            {selectedProperty ? (
              <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">

                {Object.entries({
                  [currentLanguage === 'en' ? 'Title' : 'Titulli']: selectedProperty.titulli,
                  [currentLanguage === 'en' ? 'Location' : 'Vendndodhja']: selectedProperty.location,
                  [currentLanguage === 'en' ? 'Distance from City Center' : 'Distanca nga Qendra']: selectedProperty.distanca_qendra,
                  [currentLanguage === 'en' ? 'Distance from TIA' : 'Distanca nga TIA']: selectedProperty.distanca_TIA,
                  [currentLanguage === 'en' ? 'Function' : 'Funksioni']: selectedProperty.funksioni,
                  [currentLanguage === 'en' ? 'Development Status' : 'Statusi i Zhvillimit']: selectedProperty.statusi,
                  [currentLanguage === 'en' ? 'Investment Value' : 'Vlera e Investimit']: selectedProperty.vlera,
                  [currentLanguage === 'en' ? 'Strategic Partnerships' : 'Bashkëpunime Strategjike']: selectedProperty.bashkepunime,
                  [currentLanguage === 'en' ? 'Developer' : 'Zhvilluesi']: selectedProperty.zhvilluesi,
                  [currentLanguage === 'en' ? 'Manager' : 'Menaxheri']: selectedProperty.menaxheri,
                  [currentLanguage === 'en' ? 'Beneficiary' : 'Përfituesi']: selectedProperty.perfituesi,
                  [currentLanguage === 'en' ? 'Designer' : 'Projektuesi']: selectedProperty.projektuesi,
                  [currentLanguage === 'en' ? 'Area Size (m²)' : 'Sipërfaqja e Zonës (m²)']: selectedProperty.siperfaqe_zone || "TBD",
                  [currentLanguage === 'en' ? 'Construction Area (m²)' : 'Sipërfaqja e Ndërtimit (m²)']: selectedProperty.siperfaqe_nder || "TBD",
                  [currentLanguage === 'en' ? 'Business Spaces (m²)' : 'Hapësira Biznesi (m²)']: selectedProperty.biznes || "TBD",
                  [currentLanguage === 'en' ? 'Hotel Space (m²)' : 'Hapësira Hotelerie (m²)']: selectedProperty.hoteleri || "TBD",
                  [currentLanguage === 'en' ? 'Office Space (m²)' : 'Hapësira Zyrash (m²)']: selectedProperty.zyra || "TBD",
                  [currentLanguage === 'en' ? 'Residential Space (m²)' : 'Hapësira Banimi (m²)']: selectedProperty.rezidence || "TBD",
                }).map(([key, value]) => (
                  <div key={key} className="py-2 border-b border-gray-100 last:border-0">
                    <div className="flex flex-wrap md:flex-nowrap justify-between">
                      <span className="font-medium text-gray-700 w-full md:w-auto">{key}:</span>
                      <span className="text-gray-600 w-full md:w-auto text-right">{value}</span>
                    </div>
                  </div>
                ))}
                
                {/* Image buttons */}
                <div className="mt-4 border-t pt-4">
                  <h4 className="font-medium text-gray-800 mb-2">
                    {currentLanguage === 'en' ? 'Property Images' : 'Imazhe Prone'}
                  </h4>
                  <div className="grid grid-cols-2 gap-2">
                    <button 
                      className="bg-blue-500 text-white py-2 px-3 rounded hover:bg-blue-600 transition-colors text-sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        openPhotoModal(selectedProperty.foto_projekti, 
                          `${currentLanguage === 'en' ? 'Project Photo' : 'Foto Projekti'} - ${selectedProperty.titulli}`);
                      }}
                    >
                      {currentLanguage === 'en' ? 'View Project Photo' : 'Shiko Foton e Projektit'}
                    </button>
                    <button 
                      className="bg-blue-500 text-white py-2 px-3 rounded hover:bg-blue-600 transition-colors text-sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        openPhotoModal(selectedProperty.foto_harte, 
                          `${currentLanguage === 'en' ? 'Map Photo' : 'Foto Harte'} - ${selectedProperty.titulli}`);
                      }}
                    >
                      {currentLanguage === 'en' ? 'View Map Photo' : 'Shiko Foton e Hartës'}
                    </button>
                  </div>
                </div>
                
                {/* Remove this section since we moved the PDF button to the permits section */}
                
                {/* Gallery (if available) */}
                {selectedProperty.gallery && selectedProperty.gallery.length > 0 && (
                  <div className="mt-4 border-t pt-4">
                    <h4 className="font-medium text-gray-800 mb-2">
                      {currentLanguage === 'en' ? 'Gallery' : 'Galeri'}
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {selectedProperty.gallery.map((image, index) => (
                        <button 
                          key={index}
                          className="bg-blue-500 text-white py-2 px-3 rounded hover:bg-blue-600 transition-colors text-sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            openPhotoModal(image.asset.url, 
                              `${currentLanguage === 'en' ? 'Gallery Photo' : 'Foto Galeri'} ${index + 1} - ${selectedProperty.titulli}`);
                          }}
                        >
                          {currentLanguage === 'en' ? `View Image ${index + 1}` : `Shiko Imazhin ${index + 1}`}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Development Permit, Construction Permit, and Project PDF Buttons */}
                <div className="mt-4 border-t pt-4">
                  <div className="grid grid-cols-2 gap-2">
                    <button 
                      className="bg-green-500 text-white py-2 px-3 rounded hover:bg-green-600 transition-colors text-sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        openPhotoModal(
                          selectedProperty.foto_leje_zhvillimi || selectedProperty.foto_projekti, 
                          `${currentLanguage === 'en' ? 'Development Permit' : 'Leje Zhvillimi'} - ${selectedProperty.titulli}`
                        );
                      }}
                    >
                      {currentLanguage === 'en' ? 'Development Permit' : 'Leje Zhvillimi'}
                    </button>
                    <button 
                      className="bg-green-500 text-white py-2 px-3 rounded hover:bg-green-600 transition-colors text-sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        openPhotoModal(
                          selectedProperty.foto_leje_ndertimi || selectedProperty.foto_harte, 
                          `${currentLanguage === 'en' ? 'Construction Permit' : 'Leje Ndërtimi'} - ${selectedProperty.titulli}`
                        );
                      }}
                    >
                      {currentLanguage === 'en' ? 'Construction Permit' : 'Leje Ndërtimi'}
                    </button>
                  </div>
                  
                  {/* Project PDF Button */}
                  {selectedProperty.pdf_projekti && (
                    <div className="mt-2">
                      <a 
                        href={selectedProperty.pdf_projekti}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-indigo-500 text-white py-2 px-3 rounded hover:bg-indigo-600 transition-colors text-sm block text-center"
                      >
                        {currentLanguage === 'en' ? 'View Project PDF' : 'Shiko PDF e Projektit'}
                      </a>
                    </div>
                  )}
                </div>
                
                {/* Mobile navigation buttons */}
                {isMobile && (
                  <div className="mt-4 pt-2 border-t flex justify-between">
                    <button 
                      className="bg-gray-200 text-gray-800 py-2 px-3 rounded hover:bg-gray-300 transition-colors text-sm"
                      onClick={toggleLeftSidebar}
                    >
                      {currentLanguage === 'en' ? 'Back to List' : 'Kthehu te Lista'}
                    </button>
                    <button 
                      className="bg-gray-200 text-gray-800 py-2 px-3 rounded hover:bg-gray-300 transition-colors text-sm"
                      onClick={() => setShowRightSidebar(false)}
                    >
                      {currentLanguage === 'en' ? 'View Map' : 'Shiko Hartën'}
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-500 italic">
                {currentLanguage === 'en' 
                  ? 'Select a property from the list to view details.' 
                  : 'Zgjidh një pronë nga lista për të parë detajet.'}
              </p>
            )}
          </div>
        </div>
      </main>

      {/* Mobile fixed navigation bar */}
      {isMobile && !showLeftSidebar && !showRightSidebar && (
        <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-2 flex justify-around z-20 shadow-lg">
          <button 
            className="bg-blue-500 text-white py-2 px-4 rounded-lg shadow flex items-center justify-center"
            onClick={toggleLeftSidebar}
          >
            <span className="mr-1">☰</span> 
            {currentLanguage === 'en' ? 'Projects' : 'Projektet'}
          </button>
          
          {selectedProperty && (
            <button 
              className="bg-blue-500 text-white py-2 px-4 rounded-lg shadow flex items-center justify-center"
              onClick={toggleRightSidebar}
            >
              <span className="mr-1">ℹ</span> 
              {currentLanguage === 'en' ? 'Details' : 'Detajet'}
            </button>
          )}
        </div>
      )}

      {/* Render the modal through a portal */}
      {renderModal()}
    </div>
  );
}

export default Map;