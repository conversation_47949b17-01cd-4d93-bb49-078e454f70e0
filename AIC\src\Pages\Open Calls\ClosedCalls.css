/* Base Styles (Desktop) */
.closed-calls {
  background-color: #f3f4f6;
  padding: 2rem;
  background-image: url('/public/Asset 3.png');
  background-size: 300px 300px;
  background-position: top;
  background-repeat: no-repeat;
  background-origin: content-box;
  padding-top: 80px;
}

.no-closed-calls {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
  text-align: center;
  font-size: 1.25rem;
  color: #dc2626;
  background-color: #f3f4f6;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  margin: 20px auto;
  width: 100%;
  max-width: 800px;
}

.closed-calls-header {
  text-align: center;
  margin-bottom: 1rem;
}

.closed-calls-subheading {
  color: #4b5563;
  margin-bottom: 0.5rem;
}

.closed-calls-heading {
  color: #dc2626;
  font-size: 2.25rem;
  font-weight: bold;
}

.closed-calls-heading-underline {
  width: 4rem;
  height: 0.25rem;
  background-color: #dc2626;
  margin: 0.5rem auto;
  margin-top: 20px;
}

.closed-calls-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2.5rem;
}

/* Card Styles */
.closed-calls-card {
  display: flex;
  background-color: white;
  border-radius: 2rem;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 100%;
  height: 300px;
}

/* Image container takes up 50% of the card */
.closed-calls-card-image-container {
  flex: 0 0 50%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

/* Image styling */
.closed-calls-card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  position: absolute;
  top: 0;
  left: 0;
}

/* Content container takes up 50% of the card */
.closed-calls-card-content {
  flex: 0 0 50%;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.closed-calls-card-category {
  color: #dc2626;
  font-size: 1.125rem;
  text-align: left;
  margin-bottom: 0.5rem;
}

.closed-calls-card-title {
  font-weight: bold;
  font-size: 1.25rem;
  text-align: left;
  margin-bottom: 1rem;
}

.closed-calls-card-description {
  color: #4b5563;
  font-size: 1rem;
  text-align: left;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  flex-grow: 1;
  margin-bottom: 1rem;
}

.closed-calls-card-button {
  color: #dc2626;
  font-size: 1.125rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  text-align: left;
  align-self: flex-start;
}

/* Tablet Styles */
@media (max-width: 768px) {
  .closed-calls-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .closed-calls-card {
    flex-direction: row;
    width: 100%;
    height: 250px;
  }

  .closed-calls-card-image-container {
    flex: 0 0 40%;
  }

  .closed-calls-card-content {
    flex: 0 0 60%;
  }

}

/* Mobile Styles */
@media (max-width: 480px) {
  .closed-calls {
    padding: 1rem;
    background-size: 150px 150px;
  }

  .closed-calls-heading {
    font-size: 1.5rem;
  }

  .closed-calls-heading-underline {
    width: 2.5rem;
    margin-top: 15px;
  }

  .closed-calls-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .closed-calls-card {
    flex-direction: column;
    height: auto;
    min-height: 400px;
  }

  .closed-calls-card-image-container {
    width: 100%;
    height: 200px;
    flex: none;
  }

  .closed-calls-card-content {
    width: 100%;
    flex: none;
    padding: 1rem;
  }

  .closed-calls-card-category {
    font-size: 1rem;
  }

  .closed-calls-card-title {
    font-size: 1.125rem;
  }

  .closed-calls-card-description {
    font-size: 0.875rem;
  }

  .closed-calls-card-button {
    font-size: 1rem;
  }
}