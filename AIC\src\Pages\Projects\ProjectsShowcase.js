import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../../components/languageUtils';

const ProfessionalProjectCategories = () => {
  const [selectedCategory, setSelectedCategory] = useState(null);
  const { language } = useLanguage();
  const navigate = useNavigate();

  // Category data with professional descriptions
  const categories = {
    en: [
      { 
        id: 'strategic-projects', 
        name: 'Strategic Projects', 
        route: 'strategic-projects', 
        description: "High-impact initiatives of strategic importance for Albania's long-term economic development and international competitiveness.",
        image: "/image1.jpeg"
      },
      { 
        id: 'revitalization', 
        name: 'Revitalization & Urban Renewal', 
        route: 'revitalization', 
        description: 'Comprehensive urban transformation projects that revitalize historic areas and create modern, sustainable living and working environments.',
        image: "/image2.jpeg"
      },
      { 
        id: 'real-estate', 
        name: 'Real Estate Development',
        route: 'real-estate',
        description: 'Premium property development initiatives that unlock the value of high-potential land assets and create lasting economic benefits.',
        image: "/image3.jpeg"
      }
    ],
    sq: [
      { 
        id: 'strategic-projects', 
        name: 'Projekte investimi strategjike', 
        route: 'projekte-strategjike', 
        description: 'Iniciativa me ndikim të lartë të rëndësisë strategjike për zhvillimin ekonomik afatgjatë të Shqipërisë dhe konkurrencën ndërkombëtare.',
        image: "/image1.jpeg"
      },
      { 
        id: 'revitalization', 
        name: 'Projekte investimi me Institucionet Publike',
        route: 'rindertim-zhvillim-urban', 
        description: 'Projekte gjithëpërfshirëse të transformimit urban që rijetësojnë zonat historike dhe krijojnë mjedise moderne, të qëndrueshme për jetesë dhe punë.',
        image: "/image2.jpeg"
      },
      { 
        id: 'real-estate', 
        name: 'Iniciativa të Zhvillimit të Territorit',
        route: 'projekte-te-pasurive',
        description: 'Iniciativa premium të zhvillimit të pronave që zhbllokojnë vlerën e aseteve tokësore me potencial të lartë dhe krijojnë përfitime ekonomike afatgjata.',
        image: "/image3.jpeg"
      }
    ]
  };

  const handleCategoryClick = (categoryId) => {
    setSelectedCategory(categoryId);
    
    const category = categories[language].find(cat => cat.id === categoryId);
    if (category) {
      const projectsPrefix = language === 'en' ? 'projects' : 'projekte';
      navigate(`/${language}/${projectsPrefix}/${category.route}`);
    }
  };

  // Mission statement content
  const missionStatement = language === 'en' 
    ? "AIC aims to spur economic development through engaging in complex investment projects. It was established to act as an asset manager and project developer for under-utilized public sector assets in order to promote their monetization via private capital mobilization, thereby creating a positive impact on the domestic economy."
    : "AIC synon të nxisë zhvillimin ekonomik përmes angazhimit në projekte komplekse investimi. Ajo u themelua për të vepruar si menaxher i aseteve dhe zhvillues i projekteve për asetet e sektorit publik të nënpërdorura me qëllim promovimin e monetizimit të tyre përmes mobilizimit të kapitalit privat, duke krijuar kështu një ndikim pozitiv në ekonominë vendase.";

  return (
    <div className="w-full bg-white">
      {/* Professional header section */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-4 py-16">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">
              {language === 'en' ? 'Investment Portfolio' : 'Portofoli i Investimeve'}
            </h2>
            <div className="w-24 h-1 bg-red-600 mx-auto mb-8"></div>
            <p className="text-gray-700 text-lg leading-relaxed">
              {missionStatement}
            </p>
          </div>
        </div>
      </div>

      {/* Project categories section */}
      <div className="max-w-6xl mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {categories[language].map(category => (
            <div
              key={category.id}
              className={`
                group overflow-hidden rounded-lg shadow-md transition-all duration-500 bg-white
                ${selectedCategory === category.id ? 'ring-2 ring-red-600' : ''}
              `}
            >
              {/* Image container */}
              <div 
                className="relative h-64 overflow-hidden cursor-pointer"
                onClick={() => handleCategoryClick(category.id)}
              >
                <img 
                  src={category.image} 
                  alt={category.name}
                  className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                />
                
                {/* Gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent"></div>
                
                {/* Pin marker in top-right corner */}
                <div className="absolute top-4 right-4 w-10 h-10 bg-red-600 rounded-full flex items-center justify-center shadow-lg transform group-hover:scale-110 transition-transform duration-300">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                </div>
                
                {/* Category name */}
                <div className="absolute bottom-0 left-0 right-0 p-5">
                  <h3 className="text-white text-xl md:text-2xl font-semibold">
                    {category.name}
                  </h3>
                </div>
              </div>
              
              {/* Description and button section */}
              <div className="p-5 bg-white border-t border-gray-100">
                <p className="text-gray-600 mb-5 min-h-24">
                  {category.description}
                </p>
                <button
                  onClick={() => handleCategoryClick(category.id)}
                  className={`
                    w-full py-3 rounded-md text-center font-medium transition-all duration-300
                    ${selectedCategory === category.id 
                      ? 'bg-red-600 text-white' 
                      : 'bg-gray-100 text-gray-800 hover:bg-red-600 hover:text-white'}
                  `}
                >
                  {language === 'en' ? 'View Projects' : 'Shiko Projektet'}
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProfessionalProjectCategories;