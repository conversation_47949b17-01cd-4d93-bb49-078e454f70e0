/* Base styles (desktop) */
body {
  background-color: #f3f4f6;
}

/* Base styles (desktop) */
.projects-showcase-body {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f3f4f6;
}

.projects-showcase-container {
  text-align: center;
  font-family: Arial, sans-serif;
  color: #333;
  padding: 60px;
}

.projects-showcase-header {
  background-color: #d32f2f;
  color: white;
  padding: 120px 80px 160px;
  border-radius: 80px;
  max-width: 90%;
  margin: 0 auto;
  position: relative;
  min-height: 450px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  text-align: center;
}

.projects-showcase-header-content {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  text-align: center;
  max-width: 80%;
  width: 100%;
  margin: -30px auto 0;
  padding-top: 0;
}

.projects-showcase-header h1 {
  margin: 0;
  font-size: 2.5em;
  overflow-wrap: break-word;
}
.projects-showcase-header-content p {
  margin-top: 10px;
  font-size: 1.4em;
  line-height: 1.5;
  overflow-wrap: break-word;
  color: white; /* This ensures the text is visible on the red background */
}

.projects-showcase-side-image {
  position: absolute;
  top: 0;
  height: 100%;
  width: 30%;
  z-index: 1;
}

.projects-showcase-left-image {
  left: -24%;
  clip-path: polygon(80% 0, 100% 0, 100% 100%, 80% 100%);
}

.projects-showcase-right-image {
  right: -24%;
  clip-path: polygon(0 0, 20% 0, 20% 100%, 0 100%);
}

.projects-showcase-card-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px; /* Reduced gap between cards */
  position: relative;
  top: -100px;
}

.projects-showcase-card {
  background-color: white;
  padding: 15px;
  border-radius: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0.2, 0.2);
  width: 360px; /* Reduced card size */
  height: 350px; /* Reduced card height */
  text-align: center;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-start; /* Adjusted alignment */
  align-items: center;
  z-index: 2;
}

.projects-showcase-icon {
  position: absolute;
  top: -30px;
  left: 37%;
  transform: translateX(-50%);
  width: auto;
  height: auto;
}

.projects-showcase-icon img {
  width: auto;
  height: auto;
}

.projects-showcase-image {
  width: 100%;
  height: 350px; /* Set height for image */
  overflow: hidden;
  border-radius: 15px; /* Rounded corners for image */
  margin-top: 90px; /* Space for the pin icon */
}

.projects-showcase-image img {
  width: 100%;
  height:100%;
  object-fit: cover;
}

.projects-showcase-card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  height: 50px;
  text-align: center;
  margin-top: 15px; /* Space between image and text */
}

.projects-showcase-card h3 {
  font-size: 18px;
  color: #d32f2f;
}


/* Tablet styles */
@media (max-width: 1023px) {
  .projects-showcase-container {
    padding: 40px;
  }

  .projects-showcase-header {
    padding: 80px 50px 160px;
    border-radius: 60px;
    min-height: 450px;
  }

  .projects-showcase-header-content {
    max-width: 80%;
  }

  .projects-showcase-header h1 {
    font-size: 2.2em;
  }
  .projects-showcase-card h3 {
    margin-top: -10px;
  }

  .projects-showcase-card-container {
    top: -60px;
    gap: 15px; /* Adjusted gap for smaller screens */
  }

  .projects-showcase-card {
    width: calc(50% - 15px);
    height: 400px; /* Adjusted height for smaller screens */
  }
}

/* Mobile styles */
@media (max-width: 767px) {
  .projects-showcase-container {
    padding: 20px;
  }

  .projects-showcase-header {
    padding: 60px 30px 120px;
    border-radius: 40px;
    min-height: 350px;
  }

  .projects-showcase-header-content {
    max-width: 100%;
  }

  .projects-showcase-header h1 {
    font-size: 1.8em;
  }

  .projects-showcase-header p {
    font-size: 1em;
  }

  .projects-showcase-card-container {
    top: -60px;
    gap: 10px; /* Further reduced gap for mobile */
  }

  .projects-showcase-card {
    width: 100%;
    height: 450px;
    min-height: 600px;
  }

  .projects-showcase-card h3 {
    font-size: 23px;
  }

  .projects-showcase-card p {
    font-size: 14px;
    line-height: 1.6;
    margin: 5px 0;
  }
  .projects-showcase-icon {
    position: absolute;
    top: -30px;
    left: 40%;
    transform: translateX(-50%);
    width: auto;
    height: auto;
  }
}