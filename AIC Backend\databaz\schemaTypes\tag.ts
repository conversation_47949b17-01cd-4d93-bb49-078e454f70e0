import { defineType, defineField } from 'sanity';

export default defineType({
  name: 'tag',
  title: '<PERSON><PERSON><PERSON>/Create Tags',
  type: 'document',
  fields: [
    defineField({
      name: 'language',
      title: 'Language',
      type: 'string',
      options: {
        list: [
          { title: 'English', value: 'en' },
          { title: 'Albanian', value: 'sq' }
        ],
        layout: 'radio'
      },
      validation: (Rule) => Rule.required()
    }),
    defineField({
      name: 'title',
      title: 'Tag Title',
      type: 'string',
      validation: (Rule) =>
        Rule.custom((value) => {
          if (!value) return 'Tag title is required';
          if (!value.startsWith('#')) return 'Tag must start with a "#"';
          return true;
        }),
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
        slugify: (input) => input.toLowerCase().replace(/\s+/g, '-')
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'relatedTag',
      title: 'Related Tag in Other Language',
      type: 'reference',
      to: [{ type: 'tag' }],
      options: {
        filter: ({ document }) => {
          const currentLanguage = document?.language;
          const oppositeLanguage = currentLanguage === 'en' ? 'sq' : 'en';
          return {
            filter: 'language == $oppositeLanguage && _id != $id',
            params: { 
              oppositeLanguage,
              id: document?._id
            }
          };
        }
      },
      validation: (Rule) =>
        Rule.custom((relatedTag, context) => {
          const currentLanguage = context.document?.language;
          if (!relatedTag) return true; // Allow no related tag
          
          return context
            .getClient({ apiVersion: '2021-03-25' })
            .fetch('*[_id == $id][0].language', { id: relatedTag._ref })
            .then((relatedTagLanguage) => {
              if (currentLanguage === relatedTagLanguage) {
                return 'Related tag must be in the other language';
              }
              return true;
            });
        }),
    }),
  ],
  preview: {
    select: {
      title: 'title',
      language: 'language'
    },
    prepare(selection) {
      const { title, language } = selection;
      return {
        title,
        subtitle: `Language: ${language === 'en' ? 'English' : 'Albanian'}`
      };
    }
  }
});
