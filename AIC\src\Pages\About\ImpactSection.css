.impact-section {
    max-width: 1400px;
    margin: 0 auto;
    padding: 40px 20px;
    box-sizing: border-box;
  }
  
  .impact-title {
    padding-top: 50px;
    color: #e30613; /* Red color matching the title in the image */
    font-size: 32px; /* Larger font size */
    font-weight: bold;
    margin-bottom: 30px;
  }
  
  .impact-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  
  .impact-item {
    display: flex;
    align-items: start;
    margin-bottom: 25px; /* More space between items */
    font-size: 20px; /* Larger text size */
    color: #333;
  }
  
  .impact-icon {
    margin-right: 15px;
    color: #333; /* Dark color for the icon */
    width: 24px;
    height: 24px;
    transition: color 0.3s ease, transform 0.3s ease;
  }
  
  .impact-item:hover .impact-icon {
    color: #e30613; /* Red color for the icon on hover */
    transform: scale(1.2); /* Slightly larger on hover */
  }
  