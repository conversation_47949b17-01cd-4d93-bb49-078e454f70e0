import { defineField, defineType } from 'sanity'
import { BsDatabase } from 'react-icons/bs'

export default defineType({
  name: 'openData',
  title: 'Open Data',
  type: 'document',
  icon: BsDatabase,
  fields: [
    defineField({
      name: 'customTitle_en',
      title: 'Dataset Title (English)',
      type: 'string',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'customTitle_sq',
      title: 'Dataset Title (Albanian)',
      type: 'string',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'enableLink',
      title: 'Enable Block Link',
      description: 'If enabled, the entire dataset block will be clickable',
      type: 'boolean',
      initialValue: false,
    }),
    defineField({
      name: 'categoryUrl',
      title: 'Dataset URL',
      description: 'Link to be opened when clicking the dataset block (if enabled)',
      type: 'url',
      hidden: ({ document }) => !document?.enableLink,
    }),
    defineField({
      name: 'contactInfo_en',
      title: 'Contact Information (English)',
      type: 'array',
      of: [
        {
          type: 'block',
          styles: [
            {title: 'Normal', value: 'normal'},
            {title: 'Heading', value: 'h4'}
          ],
          lists: [],
          marks: {
            decorators: [
              {title: 'Strong', value: 'strong'},
              {title: 'Emphasis', value: 'em'}
            ],
            annotations: [
              {
                title: 'URL',
                name: 'link',
                type: 'object',
                fields: [
                  {
                    title: 'URL',
                    name: 'href',
                    type: 'url'
                  }
                ]
              }
            ]
          }
        }
      ]
    }),
    defineField({
      name: 'contactInfo_sq',
      title: 'Contact Information (Albanian)',
      type: 'array',
      of: [
        {
          type: 'block',
          styles: [
            {title: 'Normal', value: 'normal'},
            {title: 'Heading', value: 'h4'}
          ],
          lists: [],
          marks: {
            decorators: [
              {title: 'Strong', value: 'strong'},
              {title: 'Emphasis', value: 'em'}
            ],
            annotations: [
              {
                title: 'URL',
                name: 'link',
                type: 'object',
                fields: [
                  {
                    title: 'URL',
                    name: 'href',
                    type: 'url'
                  }
                ]
              }
            ]
          }
        }
      ]
    }),
    defineField({
      name: 'documents',
      title: 'Data Files',
      type: 'array',
      of: [{
        type: 'object',
        title: 'Data File',
        fields: [
          defineField({
            name: 'customTitle_en',
            title: 'File Title (English)',
            type: 'string',
          }),
          defineField({
            name: 'customTitle_sq',
            title: 'File Title (Albanian)',
            type: 'string',
          }),
          defineField({
            name: 'enableLink',
            title: 'Enable Custom URL',
            description: 'If enabled, clicking will open a custom URL instead of the data file',
            type: 'boolean',
            initialValue: false,
          }),
          defineField({
            name: 'documentUrl',
            title: 'Custom URL',
            description: 'Link to be opened when clicking this data file (if enabled)',
            type: 'url',
            hidden: ({ parent }) => !parent?.enableLink,
          }),
          defineField({
            name: 'file_en',
            title: 'File (English)',
            type: 'file',
            options: {
              accept: '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv,.json,.xml'
            },
            hidden: ({ parent }) => parent?.enableLink,
          }),
          defineField({
            name: 'file_sq',
            title: 'File (Albanian)',
            type: 'file',
            options: {
              accept: '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv,.json,.xml'
            },
            hidden: ({ parent }) => parent?.enableLink,
          }),
          defineField({
            name: 'order',
            title: 'File Order',
            type: 'number',
            initialValue: 0,
          })
        ],
        preview: {
          select: {
            title_en: 'customTitle_en',
            title_sq: 'customTitle_sq',
            media: 'file_en',
            hasCustomUrl: 'enableLink'
          },
          prepare({ title_en, title_sq, hasCustomUrl }) {
            return {
              title: `${title_en} / ${title_sq}`,
              subtitle: hasCustomUrl ? '(Custom URL)' : '(Data File)'
            }
          }
        }
      }],
      options: {
        layout: 'grid'
      }
    }),
    defineField({
      name: 'order',
      title: 'Dataset Display Order',
      type: 'number',
      initialValue: 0,
    })
  ],
  preview: {
    select: {
      title_en: 'customTitle_en',
      title_sq: 'customTitle_sq',
      documentsCount: 'documents'
    },
    prepare({ title_en, title_sq, documentsCount = [] }) {
      return {
        title: `${title_en} / ${title_sq}`,
        subtitle: `${documentsCount.length} data file(s)`
      }
    }
  },
  orderings: [
    {
      title: 'Display Order',
      name: 'orderAsc',
      by: [
        {field: 'order', direction: 'asc'}
      ]
    }
  ]
})