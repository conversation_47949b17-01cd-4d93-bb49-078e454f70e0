interface BlockChild {
  _type: string;
  text?: string;
}

interface Block {
  _type: string;
  children?: BlockChild[];
}

// Helper function to extract text from block content
export const extractTextFromBlocks = (blocks: Block[]): string => {
  if (!blocks || !Array.isArray(blocks)) return '';
  
  return blocks
    .filter(block => block._type === 'block' && block.children)
    .map(block => 
      block.children!
        .filter(child => child._type === 'span' && child.text)
        .map(child => child.text!)
        .join('')
    )
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim();
};

// Helper function to truncate text
export const truncateText = (text: string, maxLength: number): string => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  
  const truncated = text.slice(0, maxLength);
  const lastSpace = truncated.lastIndexOf(' ');
  
  if (lastSpace > 0) {
    return truncated.slice(0, lastSpace) + '...';
  }
  
  return truncated + '...';
};