import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

const ScrollToTop = () => {
  const { pathname } = useLocation();

  useEffect(() => {
    const scrollToTop = () => {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    };

    // Scroll to top on route change
    scrollToTop();

    // Scroll to top on popstate event (browser back/forward)
    window.addEventListener('popstate', scrollToTop);

    // Scroll to top when clicking on any link
    const handleLinkClick = (event) => {
      if (event.target.tagName === 'A') {
        scrollToTop();
      }
    };

    document.addEventListener('click', handleLinkClick);

    // Clean up event listeners
    return () => {
      window.removeEventListener('popstate', scrollToTop);
      document.removeEventListener('click', handleLinkClick);
    };
  }, [pathname]);

  return null;
};

export default ScrollToTop;