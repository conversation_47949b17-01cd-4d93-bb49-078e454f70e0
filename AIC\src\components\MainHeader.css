@import url('https://fonts.googleapis.com/css2?family=Rubik:wght@400;700&display=swap');

.MainHeader-aicheader {
  position: relative;
  width: 100%;
  color: white;
  font-family: 'Rubik', sans-serif;
}

.MainHeader-aicheader-content {
  position: absolute;
  top: 0; 
  left: 0;
  right: 0;
  z-index: 10;
}

.MainHeader-aicheader-top-info-bar {
  display: flex;
  justify-content: flex-end;
  align-items: right;
  padding: 10px 20px;
  font-size: 14px;
}

.MainHeader-aicheader-contact-info {
  display: flex;
  gap: 20px;
}

.MainHeader-aicheader-contact-info span {
  display: flex;
  align-items: center;
}

.MainHeader-aicheader-contact-info svg {
  margin-right: 5px;  
}

.MainHeader-aicheader-invest-button {
  background-color: #e41e26;
  color: white;
  padding: 5px 15px;
  border: none;
  border-radius: 10px;
  margin-left: 20px;
  cursor: pointer;
}

.MainHeader-aicheader-social-icons {
  display: flex;
  gap: 10px;
  margin-right: 100px;
}

.MainHeader-aicheader-social-icons a {
  color: white;
  text-decoration: none;
}

.MainHeader-aicheader-main-menu {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.MainHeader-aicheader-logo img {
  height: 100px;
  width: 300px;
  object-fit: contain;
}

.MainHeader-aicheader-nav ul {
  display: flex;
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.MainHeader-aicheader-nav li {
  position: relative;
  align-items: center; /* Ensure alignment is consistent */
}

.MainHeader-aicheader-nav a,
.MainHeader-aicheader-nav span.main-menu-title {
  color: white;
  text-decoration: none;
  padding: 10px 15px;
  display: block;
  transition: background-color 0.3s ease;
  cursor: pointer; /* Ensure cursor pointer for non-clickable items */
}

.MainHeader-aicheader-nav span.main-menu-title {
  cursor: default; /* Make it non-clickable visually */
}

.MainHeader-aicheader-menu-separator {
  border-bottom: 2px solid white;
  margin: 0 20px;
}

.MainHeader-aicheader-dropdown {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #e41e26;
  border-radius: 4px;
  display: none;
  flex-direction: column;
  z-index: 1000;
  min-width: 200px;
  padding: 10px 0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.MainHeader-aicheader-nav li:hover .MainHeader-aicheader-dropdown {
  display: block;
}

.MainHeader-aicheader-dropdown ul {
  list-style: none;
  margin: 0;
  padding: 5px;
  display: flex;
  flex-direction: column;
}

.MainHeader-aicheader-dropdown ul li {
  padding: 5px;
}

.MainHeader-aicheader-dropdown ul li a {
  color: #fff;
  text-decoration: none;
  display: block;
  white-space: nowrap;
  padding: 10px 20px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.MainHeader-aicheader-dropdown ul li a:hover {
  background-color: white;
  color: #e41e26;
}

.MainHeader-aicheader-menu-toggle {
  display: none;
  position: relative;
  width: 30px;
  height: 24px;
  background: none;
  border: none;
  cursor: pointer;
  z-index: 1001;
}

.MainHeader-aicheader-menu-toggle span {
  display: block;
  position: absolute;
  height: 3px;
  width: 100%;
  background: white;
  border-radius: 3px;
  opacity: 1;
  left: 0;
  transform: rotate(0deg);
  transition: .25s ease-in-out;
}

.MainHeader-aicheader-menu-toggle span:nth-child(1) {
  top: 0px;
}

.MainHeader-aicheader-menu-toggle span:nth-child(2) {
  top: 10px;
}

.MainHeader-aicheader-menu-toggle span:nth-child(3) {
  top: 20px;
}

.MainHeader-aicheader-menu-toggle.open span:nth-child(1) {
  top: 10px;
  transform: rotate(45deg);
}

.MainHeader-aicheader-menu-toggle.open span:nth-child(2) {
  opacity: 0;
  left: -60px;
}

.MainHeader-aicheader-menu-toggle.open span:nth-child(3) {
  top: 10px;
  transform: rotate(-45deg);
}

.MainHeader-aicheader-dropdown-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding-top: 80px;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
}

.MainHeader-aicheader-dropdown-menu.open {
  transform: translateX(0);
}

.MainHeader-aicheader-dropdown-menu-content {
  width: 100%;
  padding: 0 20px;
}

.MainHeader-aicheader-dropdown-menu nav ul {
  list-style-type: none;
  padding: 0;
}

.MainHeader-aicheader-dropdown-menu nav ul li a {
  color: rgb(255, 0, 0);
  text-decoration: none;
  font-size: 24px;
  font-weight: bold;
  display: block;
  padding: 15px 0;
  text-align: left;
}

.MainHeader-aicheader-dropdown-menu-separator {
  border-top: 2px solid rgb(255, 0, 0);
  margin: 20px 0;
}

.MainHeader-aicheader-dropdown-search {
  position: relative;
  margin-bottom: 20px;
}

.MainHeader-aicheader-dropdown-search input {
  width: 100%;
  padding: 10px 40px 10px 10px;
  border: 2px solid rgb(255, 0, 0);
  border-radius: 5px;
  background-color: rgb(255, 255, 255);
  color: rgb(255, 0, 0);
}

.MainHeader-aicheader-dropdown-search svg {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: rgb(255, 5, 5);
}

.MainHeader-aicheader-dropdown-social-icons {
  display: flex;
  gap: 20px;
}

.MainHeader-aicheader-dropdown-social-icons a {
  color: rgb(255, 9, 9);
}

.MainHeader-aicheader-mobile-dropdown {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.MainHeader-aicheader-mobile-dropdown-content {
  display: none;
  padding-left: 20px;
}

.MainHeader-aicheader-mobile-dropdown-content ul {
  list-style: none;
  padding-left: 0;
}

.MainHeader-aicheader-mobile-dropdown-content ul li a {
  color: #e41e26;
  text-decoration: none;
  font-size: 20px;
  font-weight: bold;
  display: block;
  padding: 10px 0;
}

.MainHeader-aicheader-mobile-dropdown a {
  color: #e41e26;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.MainHeader-aicheader-mobile-dropdown a:hover {
  color: #d1d5db;
}

.MainHeader-aicheader-mobile-dropdown-content.open {
  display: block;
}

.MainHeader-aicheader-mobile-dropdown-toggle {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  cursor: pointer;
}

.MainHeader-aicheader-mobile-dropdown-arrow {
  transition: transform 0.3s ease;
  color: #e41e26;
  width: 40px;
  height: 40px;
}

.MainHeader-aicheader-mobile-dropdown-arrow.open {
  transform: rotate(180deg);
}

.MainHeader-aicheader-static-image {
  position: relative;
  height: 600px;
  width: 100%;
  overflow: hidden;
}

.MainHeader-aicheader-static-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.MainHeader-aicheader-static-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.MainHeader-aicheader-dynamic-content {
  position: absolute;
  top: 70%;
  left: 50px;
  transform: translateY(-50%);
  color: white;
  z-index: 2;
}

.MainHeader-aicheader-dynamic-content h1 {
  font-size: 3rem;
  margin: 0;
  text-transform: capitalize;
}

.MainHeader-aicheader-dynamic-content p {
  font-size: 1.25rem;
  margin-top: 10px;
  color: #e41e26;
}

.MainHeader-aicheader-bottom-menu {
  margin-top: -80px;
  display: flex;
  justify-content: space-around; /* Evenly spaces out the icons */
  align-items: center;
  background-color: #e41e26; /* The red background */
  height: 200px; /* Increase this value to increase the height */
  position: relative;
  z-index: 10;
  padding: 0 100px; /* Add padding to bring the icons closer together */
}

.MainHeader-aicheader-bottom-menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  text-decoration: none;
  font-size: 12px;
  transition: all 0.3s ease;
  flex: 1;
  margin: 0 5px; /* Reduce margin to bring icons closer together */
  padding: 30px; /* Add padding for better spacing inside the box */
  background-color: transparent; /* Default background color is transparent */
  position: relative; /* Needed for separator positioning */
}

/* Add white separator lines between each icon */
.MainHeader-aicheader-bottom-menu-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: -5.5px; /* Adjust to align the line properly */
  top: 50%;
  transform: translateY(-50%);
  width: 2px; /* Adjust width of the separator */
  height: 80%; /* Adjust height of the separator */
  background-color: white; /* Color of the separator line */
}

.MainHeader-aicheader-bottom-menu-item.active {
  background-color: white; /* White background for the active item */
  border-radius: 20px; /* Optional: Add rounded corners to the box */
}

.MainHeader-aicheader-bottom-menu-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px; /* Adjust width */
  height: 50px; /* Adjust height */
  margin-bottom: 5px;
  transition: all 0.3s ease;
  background-color: transparent; /* Default to transparent */
}

.MainHeader-aicheader-bottom-menu-item svg {
  color: white; /* Default icon color */
  transition: color 0.3s ease;
}

.MainHeader-aicheader-bottom-menu-item.active svg {
  color: #e41e26; /* Red icon color when active */
}

.MainHeader-aicheader-bottom-menu-item span {
  margin-top: 5px;
  transition: color 0.3s ease;
}

.MainHeader-aicheader-bottom-menu-item.active span {
  color: #e41e26; /* Red text color when active */
}

/* Adjust the height of the red line */
.MainHeader-aicheader-menu-separator {
  border-bottom: 2px solid white; /* Adjust the thickness here */
  margin: 0 20px;
}

/* Media Queries */
@media (max-width: 1024px) {
  .MainHeader-aicheader-menu-toggle {
    display: block;
    position: fixed;
    top: 15px;
    right: 20px;
    z-index: 1100;
  }

  .MainHeader-aicheader-nav,
  .MainHeader-aicheader-top-info-bar,
  .MainHeader-aicheader-invest-button,
  .MainHeader-aicheader-social-icons {
    display: none;
  }

  .MainHeader-aicheader-logo img {
    height: 60px;
    width: auto; /* Adjust width to auto for better scaling */
  }

  .MainHeader-aicheader-static-image {
    height: 300px; /* Reduced height for better fit on smaller screens */
  }

  .MainHeader-aicheader-dynamic-content {
    left: 20px;
    bottom: 20px;
    text-align: left; /* Adjust text alignment for better layout */
  }

  .MainHeader-aicheader-dynamic-content h1 {
    font-size: 2rem;
  }

  .MainHeader-aicheader-dynamic-content p {
    font-size: 1rem;
  }

  .MainHeader-aicheader-bottom-menu {
    height: 80px; /* Increased height for better touch interaction */
    padding: 0 100px; /* Adjust padding for better spacing */
  }

  .MainHeader-aicheader-bottom-menu-item {
    flex: 1; /* Ensure icons evenly distribute across the screen */
    margin: 0 2px; /* Adjust margins for tighter spacing */
  }

  .MainHeader-aicheader-bottom-menu-icon-wrapper {
    width: 40px;
    height: 40px;
  }

  .MainHeader-aicheader-bottom-menu-item span {
    font-size: 11px;
  }

  /* Adjust dropdown menu styling for mobile */
  .MainHeader-aicheader-dropdown-menu {
    padding-top: 60px; /* Reduced padding to align with smaller screen */
  }

  .MainHeader-aicheader-dropdown-menu nav ul li a {
    font-size: 20px; /* Reduced font size for mobile */
  }

  .MainHeader-aicheader-dropdown-search input {
    font-size: 14px; /* Adjusted input font size */
  }
}

@media (max-width: 768px) {
  /* Hide elements */
  .MainHeader-aicheader-top-info-bar,
  .MainHeader-aicheader-invest-button,
  .MainHeader-aicheader-social-icons,
  .MainHeader-aicheader-bottom-menu,
  .MainHeader-aicheader-nav {
    display: none; /* Hides the specified elements */
  }

  /* Adjust logo size */
  .MainHeader-aicheader-logo img {
    height: 50px;
    width: auto; /* Ensure the logo scales proportionally */
  }

  /* Reduce the static image height */
  .MainHeader-aicheader-static-image {
    height: 250px; /* Adjust height for smaller screens */
  }

  /* Adjust dynamic content positioning */
  .MainHeader-aicheader-dynamic-content {
    left: 15px;
    bottom: 15px;
    text-align: left; /* Ensure text is aligned for readability */
  }

  .MainHeader-aicheader-dynamic-content h1 {
    font-size: 1.75rem; /* Adjust font size for better readability */
  }

  .MainHeader-aicheader-dynamic-content p {
    font-size: 1rem;
    margin-top: 5px; /* Reduced margin for tighter spacing */
  }

  /* Adjust dropdown menu for mobile */
  .MainHeader-aicheader-dropdown-menu {
    padding-top: 60px; /* Ensure the dropdown starts below the header */
  }

  .MainHeader-aicheader-dropdown-menu nav ul li a {
    font-size: 18px; /* Reduced font size for better fit */
    padding: 10px 0; /* Adjusted padding for cleaner layout */
  }

  .MainHeader-aicheader-dropdown-search input {
    font-size: 14px; /* Keep the input text legible on small screens */
  }

  .MainHeader-aicheader-mobile-dropdown-content ul li a {
    font-size: 16px; /* Slightly larger font for mobile readability */
    padding: 8px 0; /* Adjust padding for better touch interaction */
  }
}

@media (max-width: 480px) {
  /* Further hide or adjust elements */
  .MainHeader-aicheader-static-image {
    height: 200px; /* Further reduced height for very small screens */
  }

  .MainHeader-aicheader-logo img {
    height: 40px; /* Smaller logo for compact screens */
  }

  .MainHeader-aicheader-dynamic-content {
    left: 10px;
    bottom: 10px;
  }

  .MainHeader-aicheader-dynamic-content h1 {
    font-size: 1.5rem; /* Adjusted font size for readability */
  }

  .MainHeader-aicheader-dynamic-content p {
    font-size: 0.9rem;
    margin-top: 5px;
  }

  .MainHeader-aicheader-mobile-dropdown-content ul li a {
    font-size: 14px; /* Further reduced font size for small screens */
    padding: 6px 0; /* Tighter padding for better layout */
  }
}
