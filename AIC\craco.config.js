const webpack = require('webpack');
const TerserPlugin = require('terser-webpack-plugin');
const WebpackObfuscator = require('webpack-obfuscator');

module.exports = {
  webpack: {
    configure: (webpackConfig) => {
      // Disable source maps
      webpackConfig.devtool = false;

      // Existing fallback configuration
      webpackConfig.resolve.fallback = {
        ...webpackConfig.resolve.fallback,
        crypto: require.resolve('crypto-browserify'),
        buffer: require.resolve('buffer'),
        process: require.resolve('process/browser.js'),
      };

      // Combine existing and new plugins
      webpackConfig.plugins = (webpackConfig.plugins || []).concat([
        new webpack.ProvidePlugin({
          Buffer: ['buffer', 'Buffer'],
          process: 'process/browser.js',
        }),
        new WebpackObfuscator({
          rotateStringArray: true,
          stringArray: true,
          stringArrayEncoding: ['base64'],
          splitStrings: true,
          identifierNamesGenerator: 'hexadecimal'
        })
      ]);

      // Add Terser configuration for production
      webpackConfig.optimization.minimizer = [
        new TerserPlugin({
          terserOptions: {
            compress: {
              drop_console: true,
              dead_code: true
            },
            format: {
              comments: false,
            },
          },
          extractComments: false,
        }),
      ];

      // Keep your existing CI configuration
      if (process.env.CI) {
        webpackConfig.stats = {
          warnings: false,
        };
      }

      return webpackConfig;
    },
  },
  // Keep your existing Babel configuration
  babel: {
    plugins: [
      ['@babel/plugin-proposal-private-property-in-object', { loose: true }],
      ['@babel/plugin-proposal-class-properties', { loose: true }],
      ['@babel/plugin-transform-class-properties', { loose: true }],
    ],
  },
  // Keep your existing ESLint configuration
  eslint: {
    enable: true,
    mode: "extends",
  },
};