import {StructureBuilder} from 'sanity/desk'
import {
  BsNewspaper, 
  BsCalendarEvent, 
  BsMegaphone,
  BsFolder2Open, 
  BsHouseDoor, 
  BsFileEarmarkText, 
  BsPeople, 
  BsTag,
  BsTranslate,
  BsGlobe,
  BsWindow,
  BsDisplay,
  BsFileEarmark,
  BsListNested,
  BsFolder,
  BsDatabase
} from 'react-icons/bs'

export const myStructure = (S: StructureBuilder) =>
  S.list()
    .title('Përmbajtja')
    .items([
      // Lajme & Ngjarje - HAS language field
      S.listItem()
        .title('Lajme & Ngjarje')
        .icon(BsNewspaper)
        .child(
          S.list()
            .title('<PERSON>j<PERSON> & Ngjarje')
            .items([
              S.listItem()
                .title('Lajme')
                .icon(BsNewspaper)
                .child(
                  S.list()
                    .title('Lajme')
                    .items([
                      S.listItem()
                        .title('Lajmet Shqip')
                        .icon(BsTranslate)
                        .child(S.documentList()
                          .title('Lajmet Shqip')
                          .filter('_type == "newsPost" && language == "sq"')
                        ),
                      S.listItem()
                        .title('News English')
                        .icon(BsGlobe)
                        .child(S.documentList()
                          .title('News English')
                          .filter('_type == "newsPost" && language == "en"')
                        )
                    ])
                ),
              S.listItem()
                .title('Ngjarje')
                .icon(BsCalendarEvent)
                .child(
                  S.list()
                    .title('Ngjarje')
                    .items([
                      S.listItem()
                        .title('Ngjarjet Shqip')
                        .icon(BsTranslate)
                        .child(S.documentList()
                          .title('Ngjarjet Shqip')
                          .filter('_type == "eventsPost" && language == "sq"')
                        ),
                      S.listItem()
                        .title('Events English')
                        .icon(BsGlobe)
                        .child(S.documentList()
                          .title('Events English')
                          .filter('_type == "eventsPost" && language == "en"')
                        )
                    ])
                ),
              S.listItem()
                .title('Njoftime')
                .icon(BsMegaphone)
                .child(
                  S.list()
                    .title('Njoftime')
                    .items([
                      S.listItem()
                        .title('Njoftimet Shqip')
                        .icon(BsTranslate)
                        .child(S.documentList()
                          .title('Njoftimet Shqip')
                          .filter('_type == "noticesPost" && language == "sq"')
                        ),
                      S.listItem()
                        .title('Notices English')
                        .icon(BsGlobe)
                        .child(S.documentList()
                          .title('Notices English')
                          .filter('_type == "noticesPost" && language == "en"')
                        )
                    ])
                ),
            ])
        ),

      // Projekte & Thirrje - HAS language field
S.listItem()
.title('Projekte & Thirrje')
.icon(BsFolder2Open)
.child(
  S.list()
    .title('Projekte & Thirrje')
    .items([
      S.listItem()
        .title('Projekte')
        .child(
          S.list()
            .title('Projekte')
            .items([
              S.listItem()
                .title('Projektet Shqip')
                .icon(BsTranslate)
                .child(S.documentList()
                  .title('Projektet Shqip')
                  .filter('_type == "projectsPost" && isProject == true && language == "sq"')
                ),
              S.listItem()
                .title('Projects English')
                .icon(BsGlobe)
                .child(S.documentList()
                  .title('Projects English')
                  .filter('_type == "projectsPost" && isProject == true && language == "en"')
                )
            ])
        ),
      S.listItem()
        .title('Thirrje')
        .child(
          S.list()
            .title('Thirrje')
            .items([
              S.listItem()
                .title('Thirrjet Shqip')
                .icon(BsTranslate)
                .child(S.documentList()
                  .title('Thirrjet Shqip')
                  .filter('_type == "projectsPost" && isCall == true && language == "sq"')
                ),
              S.listItem()
                .title('Calls English')
                .icon(BsGlobe)
                .child(S.documentList()
                  .title('Calls English')
                  .filter('_type == "projectsPost" && isCall == true && language == "en"')
                )
            ])
        ),
    ])
),

      // Pronat - NO language field
      S.listItem()
        .title('Harta')
        .icon(BsHouseDoor)
        .child(
          S.documentTypeList('property')
            .title('Harta')
        ),

      // Dokumente & Burime - NO language field
      S.listItem()
        .title('Programi Transparences')
        .icon(BsFileEarmarkText)
        .child(
          S.list()
            .title('Programi Transparences')
            .items([
              S.listItem()
                .title('Kategoritë e Dokumenteve')
                .child(
                  S.documentTypeList('documentCategory')
                    .title('Kategoritë e Dokumenteve')
                ),
              // Updated Ligje & Rregullore section with new schema types
              S.listItem()
                .title('Ligje & Rregullore')
                .icon(BsFileEarmark)
                .child(
                  S.list()
                    .title('Ligje & Rregullore')
                    .items([
                      S.listItem()
                        .title('Kategoritë')
                        .icon(BsFolder)
                        .child(
                          S.documentTypeList('lawpdfCategory')
                            .title('Kategoritë')
                        ),
                      S.listItem()
                        .title('Nënkategoritë')
                        .icon(BsFolder)
                        .child(
                          S.documentTypeList('lawpdfSubcategory')
                            .title('Nënkategoritë')
                        ),
                      S.listItem()
                        .title('Kategoritë e tretë')
                        .icon(BsFolder)
                        .child(
                          S.documentTypeList('lawpdfTertiaryCategory')
                            .title('Kategoritë e tretë')
                        ),
                      S.listItem()
                        .title('Dokumentet')
                        .icon(BsFileEarmarkText)
                        .child(
                          S.documentTypeList('lawpdfItem')
                            .title('Dokumentet')
                        ),
                    ])
                ),
            ])
        ),

      // Të Dhënat e Hapura - HAS language field
      S.listItem()
        .title('Të Dhënat e Hapura')
        .icon(BsDatabase)
        .child(
          S.list()
            .title('Të Dhënat e Hapura')
            .items([
              S.listItem()
                .title('Të Dhënat Shqip')
                .icon(BsTranslate)
                .child(S.documentList()
                  .title('Të Dhënat Shqip')
                  .filter('_type == "openData" && language == "sq"')
                ),
              S.listItem()
                .title('Open Data English')
                .icon(BsGlobe)
                .child(S.documentList()
                  .title('Open Data English')
                  .filter('_type == "openData" && language == "en"')
                ),
              S.listItem()
                .title('Të gjitha / All')
                .icon(BsDatabase)
                .child(
                  S.documentTypeList('openData')
                    .title('Të gjitha të dhënat / All Open Data')
                )
            ])
        ),

      // Ekipi & Karriera - Karriera HAS language field, Ekipi doesn't 
      S.listItem()
        .title('Ekipi & Karriera')
        .icon(BsPeople)
        .child(
          S.list()
            .title('Ekipi & Karriera')
            .items([
              S.listItem()
                .title('Anëtarët e Ekipit')
                .child(
                  S.documentTypeList('teamMember')
                    .title('Anëtarët e Ekipit')
                ),
              S.listItem()
                .title('Postime Karriere')
                .child(
                  S.list()
                    .title('Postime Karriere')
                    .items([
                      S.listItem()
                        .title('Karriera Shqip')
                        .icon(BsTranslate)
                        .child(S.documentList()
                          .title('Karriera Shqip')
                          .filter('_type == "careerPost" && language == "sq"')
                        ),
                      S.listItem()
                        .title('Careers English')
                        .icon(BsGlobe)
                        .child(S.documentList()
                          .title('Careers English')
                          .filter('_type == "careerPost" && language == "en"')
                        )
                    ])
                ),
            ])
        ),

      // Etiketat - HAS language field
      S.listItem()
        .title('Etiketat')
        .icon(BsTag)
        .child(
          S.list()
            .title('Etiketat')
            .items([
              S.listItem()
                .title('Etiketat Shqip')
                .icon(BsTranslate)
                .child(S.documentList()
                  .title('Etiketat Shqip')
                  .filter('_type == "tag" && language == "sq"')
                ),
              S.listItem()
                .title('Tags English')
                .icon(BsGlobe)
                .child(S.documentList()
                  .title('Tags English')
                  .filter('_type == "tag" && language == "en"')
                )
            ])
        ),
        
      S.listItem()
        .title('Popup')
        .icon(BsWindow)
        .child(
          S.documentTypeList('welcomePopup')
            .title('Welcome Popup')
        ),

      // Show any remaining document types - updated to exclude new schema types
      ...S.documentTypeListItems()
        .filter(listItem => ![
          'newsPost', 'eventsPost', 'noticesPost', 'careerPost', 
          'projectsPost', 'property', 'documentCategory', 
          // Removed: 'multiplePdfDocument', 
          // Added new schema types:
          'lawpdfCategory', 'lawpdfSubcategory', 'lawpdfTertiaryCategory', 'lawpdfItem',
          'openData', // Open Data schema type
          'teamMember', 'tag', 'welcomePopup'
        ].includes(listItem.getId() || ''))
    ])