// markAsDeleted.js
const {createClient} = require('@sanity/client')

const client = createClient({
  projectId: "1ag8k8v1", // Your project ID
  dataset: 'production2',
  token: 'your-api-token-here', // Replace with your actual API token if needed
  useCdn: false,
  apiVersion: '2023-05-03' // Use the latest API version
})

const documentId = 'drafts.56c2bfd2-0794-43ca-a973-f39d1b495c4e'

client
  .patch(documentId)
  .set({isDeleted: true})
  .commit()
  .then(() => {
    console.log(`Successfully marked document with ID: ${documentId} as deleted`)
    process.exit(0)
  })
  .catch(err => {
    console.error(`Failed to mark document as deleted: ${err.message}`)
    process.exit(1)
  })