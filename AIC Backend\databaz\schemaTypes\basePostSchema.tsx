import { defineField, Rule, ValidationBuilder } from 'sanity'
import { StringInputProps, TextInputProps, useFormValue } from 'sanity';
import { set, unset } from 'sanity';
import React from 'react';

interface SanityDocument {
  _type?: string;
  language?: string;
  title?: string;
}

// Helper function to extract text from portable text blocks
const extractTextFromBlocks = (blocks: any[]): string => {
  if (!blocks || !Array.isArray(blocks)) return '';
  
  return blocks
    .filter(block => block._type === 'block')
    .map(block => {
      if (block.children) {
        return block.children
          .map((child: any) => child.text || '')
          .join('');
      }
      return '';
    })
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim();
};

// Function to generate meta title from document title
const generateMetaTitle = (document: any): string => {
  const title = document?.title || '';
  if (!title) return '';
  
  // Truncate to 60 characters if too long
  if (title.length <= 60) return title;
  return title.slice(0, 57) + '...';
};

// Function to generate meta description from document body
const generateMetaDescription = (document: any): string => {
  const bodyText = extractTextFromBlocks(document?.body || []);
  if (!bodyText) return '';
  
  // Truncate to 160 characters if too long
  if (bodyText.length <= 160) return bodyText;
  return bodyText.slice(0, 157) + '...';
};

// Custom Meta Title Input Component
const MetaTitleInput = React.forwardRef<HTMLInputElement, StringInputProps>((props, ref) => {
  const { onChange, value, elementProps } = props;
  
  // Use Sanity's hook to access form values
  const documentTitle = useFormValue(['title']) as string;
  
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = event.target.value;
    onChange(inputValue ? set(inputValue) : unset());
  };

  const handleGenerate = () => {
    const generatedTitle = generateMetaTitle({ title: documentTitle });
    onChange(generatedTitle ? set(generatedTitle) : unset());
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
      <div style={{ display: 'flex', gap: '8px', alignItems: 'flex-end' }}>
        <div style={{ flex: 1 }}>
          <input
            {...elementProps}
            ref={ref}
            value={value || ''}
            onChange={handleChange}
            placeholder="Enter meta title or click generate"
            style={{
              width: '100%',
              padding: '8px 12px',
              border: '1px solid #ddd',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          />
        </div>
        <button
          type="button"
          onClick={handleGenerate}
          disabled={!documentTitle}
          style={{
            padding: '8px 16px',
            backgroundColor: documentTitle ? '#0066cc' : '#ccc',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: documentTitle ? 'pointer' : 'not-allowed',
            fontSize: '14px',
            whiteSpace: 'nowrap'
          }}
        >
          Generate
        </button>
      </div>
      <div style={{ fontSize: '12px', color: '#666' }}>
        {value ? `${value.length}/60 characters` : '0/60 characters'}
      </div>
    </div>
  );
});

// Custom Meta Description Input Component
const MetaDescriptionInput = React.forwardRef<HTMLTextAreaElement, TextInputProps>((props, ref) => {
  const { onChange, value, elementProps } = props;
  
  // Use Sanity's hook to access form values
  const documentBody = useFormValue(['body']) as any[];
  
  const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const inputValue = event.target.value;
    onChange(inputValue ? set(inputValue) : unset());
  };

  const handleGenerate = () => {
    const generatedDescription = generateMetaDescription({ body: documentBody });
    onChange(generatedDescription ? set(generatedDescription) : unset());
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
      <div style={{ display: 'flex', gap: '8px', alignItems: 'flex-start' }}>
        <div style={{ flex: 1 }}>
          <textarea
            {...elementProps}
            ref={ref}
            value={value || ''}
            onChange={handleChange}
            placeholder="Enter meta description or click generate"
            rows={3}
            style={{
              width: '100%',
              padding: '8px 12px',
              border: '1px solid #ddd',
              borderRadius: '4px',
              fontSize: '14px',
              resize: 'vertical',
              minHeight: '80px'
            }}
          />
        </div>
        <button
          type="button"
          onClick={handleGenerate}
          disabled={!documentBody || !Array.isArray(documentBody) || documentBody.length === 0}
          style={{
            padding: '8px 16px',
            backgroundColor: (documentBody && Array.isArray(documentBody) && documentBody.length > 0) ? '#0066cc' : '#ccc',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: (documentBody && Array.isArray(documentBody) && documentBody.length > 0) ? 'pointer' : 'not-allowed',
            fontSize: '14px',
            whiteSpace: 'nowrap',
            alignSelf: 'flex-start'
          }}
        >
          Generate
        </button>
      </div>
      <div style={{ fontSize: '12px', color: '#666' }}>
        {value ? `${value.length}/160 characters` : '0/160 characters'}
      </div>
    </div>
  );
});

const basePostSchema = {
  fields: [
    defineField({
      name: 'language',
      title: 'Language',
      type: 'string',
      options: {
        list: [
          { title: 'English', value: 'en' },
          { title: 'Albanian', value: 'sq' }
        ],
        layout: 'radio'
      },
      validation: (Rule: ValidationBuilder) => Rule.required()
    }),
    defineField({
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: ValidationBuilder) => Rule.required()
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',  // Simplified to just use the title directly
        maxLength: 150,
        slugify: (input: string) => 
          input
            .toLowerCase()
            .replace(/\s+/g, '-')
            .replace(/[^a-z0-9-\/]/g, '')
            .replace(/-+/g, '-')
            .slice(0, 200)
      },
      validation: (Rule: ValidationBuilder) => Rule.required()
    }),
    defineField({
      name: 'publishDate',
      title: 'Publish Date',
      type: 'date',
      options: {
        dateFormat: 'DD-MM-YYYY',
      },
    }),
    defineField({
      name: 'mainImage',
      title: 'Main Image',
      type: 'image',
      options: {
        hotspot: true,
      },
    }),
    defineField({
      name: 'body',
      title: 'Body',
      type: 'blockContent',
    }),
    defineField({
      name: 'gallery',
      title: 'Gallery',
      type: 'array',
      of: [{ type: 'image', options: { hotspot: true } }],
    }),
    defineField({
      name: 'pdfFiles',
      title: 'PDF Files',
      type: 'array',
      of: [
        {
          type: 'file',
          options: {
            accept: 'application/pdf',
          },
          fields: [
            {
              name: 'customName',
              title: 'Custom Name',
              type: 'string',
            },
          ],
        },
      ],
    }),
    defineField({
      name: 'tags',
      title: 'Tags',
      type: 'array',
      of: [{ type: 'reference', to: [{ type: 'tag' }] }],
    }),
    defineField({
      name: 'metaTitle',
      title: 'Meta Title',
      type: 'string',
      description: 'SEO title for search engines (max 60 characters). Click generate to auto-populate from title.',
      components: {
        input: MetaTitleInput,
      },
      validation: (Rule: ValidationBuilder) => Rule.max(60).warning('Meta title should be 60 characters or less')
    }),
    defineField({
      name: 'metaDescription',
      title: 'Meta Description',
      type: 'text',
      description: 'SEO description for search engines (max 160 characters). Click generate to auto-populate from content.',
      rows: 3,
      components: {
        input: MetaDescriptionInput,
      },
      validation: (Rule: ValidationBuilder) => Rule.max(160).warning('Meta description should be 160 characters or less')
    }),
    defineField({
      name: 'translationRef',
      title: 'Translation Reference',
      type: 'reference',
      to: [
        { type: 'newsPost' },
        { type: 'eventsPost' },
        { type: 'noticesPost' },
        { type: 'careerPost' }
      ],
      description: 'Reference to the translated version of this post',
      options: {
        filter: ({ document }: { document: Partial<SanityDocument> | null }) => {
          if (!document) return { filter: '' };
          const oppositeLanguage = document.language === 'en' ? 'sq' : 'en';
          return {
            filter: `language == $oppositeLanguage && _type == $postType`,
            params: {
              oppositeLanguage,
              postType: document._type
            }
          };
        }
      },
      validation: (Rule: ValidationBuilder) => Rule.custom((translationRef: any, context: any) => {
        if (!context.document) return true;
        if (translationRef && translationRef._ref) {
          const oppositeLanguage = context.document.language === 'en' ? 'sq' : 'en';
          return context.getClient({ apiVersion: '2021-06-07' })
            .fetch(`*[_id == $id][0].language`, { id: translationRef._ref })
            .then((relatedLanguage: string) => {
              if (relatedLanguage !== oppositeLanguage) {
                return 'The translation reference must be in the opposite language';
              }
              return true;
            });
        }
        return true;
      })
    }),
  ],
  preview: {
    select: {
      title: 'title',
      date: 'publishDate',
      media: 'mainImage',
      language: 'language',
      slug: 'slug',
    },
    prepare(selection: { title?: string; date?: string; media?: any; language?: string; slug?: { current: string } }) {
      const { title, date, media, language, slug } = selection;
      return {
        title: title || 'Untitled',
        subtitle: `${language?.toUpperCase() || 'No language'} - ${slug?.current || 'No slug'} - ${date 
          ? new Date(date).toLocaleDateString('en-GB', {
              day: 'numeric', 
              month: 'long', 
              year: 'numeric'
            })
          : 'No date set'}`,
        media: media
      }
    }
  },
};

export default basePostSchema;