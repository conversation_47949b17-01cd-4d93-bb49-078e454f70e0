@import url('https://fonts.googleapis.com/css2?family=Rubik:wght@400;700&display=swap');

.headerhome-aicheader {
  position: relative;
  width: 100%;
  color: white;
  font-family: 'Rubik', sans-serif;
}

.headerhome-aicheader-content {
  position: absolute;
  top: 0; 
  left: 0;
  right: 0;
  z-index: 10;
}

.headerhome-aicheader-top-info-bar {
  display: flex;
  justify-content: flex-end;
  align-items: right;
  padding: 10px 20px;
  font-size: 14px;
}

.headerhome-aicheader-contact-info {
  display: flex;
  gap: 20px;
}

.headerhome-aicheader-contact-info span {
  display: flex;
  align-items: center;
}

.headerhome-aicheader-contact-info svg {
  margin-right: 5px;  
}

.headerhome-aicheader-invest-button {
  background-color: #e41e26;
  color: white;
  padding: 5px 15px;
  border: none;
  border-radius: 10px;
  margin-left: 20px;
  cursor: pointer;
}

.headerhome-aicheader-social-icons {
  display: flex;
  gap: 10px;
  margin-right: 100px;
}

.headerhome-aicheader-social-icons a {
  color: white;
  text-decoration: none;
}

.headerhome-aicheader-main-menu {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px;
}

.headerhome-aicheader-logo img {
  height: 100px;
  width: 300px;
  object-fit: contain;
}

.headerhome-aicheader-nav ul {
  display: flex;
  list-style-type: none;
  padding: 0;
}

.headerhome-aicheader-nav li {
  position: relative;
  display: flex;
  align-items: center;
}

.headerhome-aicheader-nav a, 
.headerhome-aicheader-nav span.non-clickable {
  color: white;
  text-decoration: none;
  padding: 10px 15px;
  display: block;
  transition: background-color 0.3s ease;
}

.headerhome-aicheader-nav a:hover, 
.headerhome-aicheader-nav span.non-clickable:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.headerhome-aicheader-mobile-dropdown a, 
.headerhome-aicheader-mobile-dropdown span.non-clickable {
  color: #e41e26;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.headerhome-aicheader-mobile-dropdown a:hover, 
.headerhome-aicheader-mobile-dropdown span.non-clickable:hover {
  color: #d1d5db;
}

.headerhome-aicheader-menu-separator {
  border-bottom: 2px solid white;
  margin: 0 20px;
}

.headerhome-aicheader-dropdown {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #e41e26;
  border-radius: 4px;
  display: none;
  flex-direction: column;
  z-index: 1000;
  min-width: 200px;
  padding: 10px 0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.headerhome-aicheader-nav li:hover .headerhome-aicheader-dropdown {
  display: block;
}

.headerhome-aicheader-dropdown ul {
  list-style: none;
  margin: 0;
  padding: 5px;
  display: flex;
  flex-direction: column;
}

.headerhome-aicheader-dropdown ul li {
  padding: 5px;
}

.headerhome-aicheader-dropdown ul li a {
  color: #fff;
  text-decoration: none;
  display: block;
  white-space: nowrap;
  padding: 10px 20px;
  transition: background-color 0.3s ease, color 0.3s ease;
  width: 100%;
}

.headerhome-aicheader-dropdown ul li a:hover {
  background-color: white;
  color: #e41e26;
}

.headerhome-aicheader-menu-toggle {
  display: none;
  position: relative;
  width: 30px;
  height: 24px;
  background: none;
  border: none;
  cursor: pointer;
  z-index: 1001;
}

.headerhome-aicheader-menu-toggle span {
  display: block;
  position: absolute;
  height: 3px;
  width: 100%;
  background: white;
  border-radius: 3px;
  opacity: 1;
  left: 0;
  transform: rotate(0deg);
  transition: .25s ease-in-out;
}

.headerhome-aicheader-menu-toggle span:nth-child(1) {
  top: 0px;
}

.headerhome-aicheader-menu-toggle span:nth-child(2) {
  top: 10px;
}

.headerhome-aicheader-menu-toggle span:nth-child(3) {
  top: 20px;
}

.headerhome-aicheader-menu-toggle.open span:nth-child(1) {
  top: 16px;
  transform: rotate(45deg) scale(1.2);
}

.headerhome-aicheader-menu-toggle.open span:nth-child(2) {
  opacity: 0;
}

.headerhome-aicheader-menu-toggle.open span:nth-child(3) {
  top: 16px;
  transform: rotate(-45deg) scale(1.2);
}

.headerhome-aicheader-dropdown-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding-top: 80px;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
}

.headerhome-aicheader-dropdown-menu.open {
  transform: translateX(0);
}

.headerhome-aicheader-dropdown-menu-content {
  width: 100%;
  padding: 0 20px;
}

.headerhome-aicheader-dropdown-menu nav ul {
  list-style-type: none;
  padding: 0;
}

.headerhome-aicheader-dropdown-menu nav ul li a {
  color: rgb(255, 0, 0);
  text-decoration: none;
  font-size: 24px;
  font-weight: bold;
  display: block;
  padding: 15px 0;
  text-align: left;
}

.headerhome-aicheader-dropdown-menu-separator {
  border-top: 2px solid rgb(255, 0, 0);
  margin: 20px 0;
}

.headerhome-aicheader-dropdown-search {
  position: relative;
  margin-bottom: 20px;
}

.headerhome-aicheader-dropdown-search input {
  width: 100%;
  padding: 10px 40px 10px 10px;
  border: 2px solid rgb(255, 0, 0);
  border-radius: 5px;
  background-color: rgb(255, 255, 255);
  color: rgb(255, 0, 0);
}

.headerhome-aicheader-dropdown-search svg {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: rgb(255, 5, 5);
}

.headerhome-aicheader-dropdown-social-icons {
  display: flex;
  gap: 20px;
}

.headerhome-aicheader-dropdown-social-icons a {
  color: rgb(255, 9, 9);
}

.headerhome-aicheader-exit-button {
  display: none;
  position: fixed;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  color: rgb(255, 0, 0);
  font-size: 30px;
  cursor: pointer;
  z-index: 1100;
}

.headerhome-aicheader-mobile-dropdown {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.headerhome-aicheader-mobile-dropdown-content {
  display: none;
  padding-left: 20px;
}

.headerhome-aicheader-mobile-dropdown-content ul {
  list-style: none;
  padding-left: 0;
}

.headerhome-aicheader-mobile-dropdown-content ul li a {
  color: #e41e26;
  text-decoration: none;
  font-size: 20px;
  font-weight: bold;
  display: block;
  padding: 10px 0;
}

.headerhome-aicheader-mobile-dropdown a {
  color: #e41e26;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.headerhome-aicheader-mobile-dropdown a:hover {
  color: #d1d5db;
}

.headerhome-aicheader-mobile-dropdown-content.open {
  display: block;
}

.headerhome-aicheader-mobile-dropdown-toggle {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  cursor: pointer;
}

.headerhome-aicheader-mobile-dropdown-arrow {
  transition: transform 0.3s ease;
  color: #e41e26;
  width: 40px;
  height: 40px;
}

.headerhome-aicheader-mobile-dropdown-arrow.open {
  transform: rotate(180deg);
}

.headerhome-aicheader-mobile-dropdown-content ul li a {
  color: #e41e26;
  text-decoration: none;
  font-size: 20px;
  font-weight: bold;
  display: block;
  padding: 10px 0;
}

.headerhome-aicheader-slider {
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.headerhome-aicheader-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 1s ease-in-out;
}

.headerhome-aicheader-slide.active {
  opacity: 1;
}

.headerhome-aicheader-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.headerhome-aicheader-slide-content {
  position: absolute;
  bottom: 0; /* Align with bottom of slider */
  left: 50%; /* Center horizontally */
  transform: translateX(-50%); /* Center adjustment */
  width: 70%; /* Match width of red boxes */
  padding: 390px 0; /* Vertical padding only */
  z-index: 2;
  text-align: left;
  color: white;
}


.headerhome-aicheader-empowering-text {
  color: #ffffff;
  font-weight: 600;
  margin-bottom: 10px;
  font-size: clamp(1rem, 2vw, 1.25rem);
  position: relative;
}

.headerhome-aicheader-empowering-text::after {
  content: '';
  display: block;
  width: 80px;
  height: 10px;
  background-color: #e41e26;
  position: absolute;
  bottom: -15px;
  left: 3%;
  transform: translateX(-50%);
}

.headerhome-aicheader-slide-content h1 {
  font-size: clamp(2rem, 4vw, 3.25rem);
  font-weight: bold;
  line-height: 1.2;
  margin-top: 30px;
}

.headerhome-aicheader-slide-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.headerhome-aicheader-next-slide-button {
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0.8;
  color: #d1d5db;
}

.headerhome-aicheader-next-slide-button:hover {
  opacity: 1;
}

.headerhome-aicheader-red-boxes {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 70%;
  background-color: #e41e26;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4rem 0; /* Reduced padding to adjust height */
  z-index: 20;
  border-top-left-radius: 30px;
  border-top-right-radius: 30px;
}

.headerhome-aicheader-feature {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 0 0.5rem;
  position: relative;
}

.headerhome-aicheader-feature:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 10%;
  height: 80%;
  width: 2px;
  background-color: rgba(255, 255, 255, 0.3);
}

.headerhome-aicheader-feature-icon {
  margin-bottom: 0.75rem;
  font-size: 2rem;
}

.headerhome-aicheader-feature-text {
  font-size: 0.75rem;
  text-transform: uppercase;
  max-width: 120px;
  line-height: 1.2;
}

/* Media Queries */
@media (max-width: 1366px) {
  .headerhome-aicheader-slide-content {
    right: 45%;
    width: 55%;
  }

  .headerhome-aicheader-slide-content h1 {
    font-size: clamp(1.8rem, 3.5vw, 2.8rem);
  }
}

@media (max-width: 1100px) {
  .headerhome-aicheader-menu-toggle {
    display: flex;
    justify-content: flex-end;
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1100;
  }

  .headerhome-aicheader-exit-button {
    display: block;
    position: absolute;
    top: 20px;
    right: 20px;
  }

  .headerhome-aicheader-nav,
  .headerhome-aicheader-top-info-bar,
  .headerhome-aicheader-invest-button,
  .headerhome-aicheader-social-icons {
    display: none;
  }

  .headerhome-aicheader-logo img {
    height: 60px;
    width: 180px;
  }

  .headerhome-aicheader-slide-content {
    padding: 20px;
    text-align: left;
    left: 50%;
    right: auto;
    transform: translate(-50%, -50%);
    width: 80%;
  }

  .headerhome-aicheader-empowering-text {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 1.25rem;
    position: relative;
  }
  
  .headerhome-aicheader-empowering-text::after {
    content: '';
    display: block;
    width: 80px;
    height: 10px;
    background-color: #e41e26;
    position: absolute;
    bottom: -15px;
    left: 5%;
    transform: translateX(-50%);
  }
  
  .headerhome-aicheader-slide-content h1 {
    margin-top: 20px;
    font-size: clamp(1.6rem, 3vw, 2.5rem);
    font-weight: bold;
    margin-bottom: 100px;
  }
  
  .headerhome-aicheader-red-boxes {
    width: 90%;
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .headerhome-aicheader-feature {
    width: 33.33%;
    padding: 1rem 0.5rem;
  }
  
  .headerhome-aicheader-feature:nth-child(3n)::after {
    display: none;
  }
}

@media (max-width: 768px) {
  .headerhome-aicheader-slider {
    height: 70vh;
  }
  .headerhome-aicheader-logo img {
    height: 80px;
    width: 240px;
  }
  .headerhome-aicheader-slide-content {
    width: 90%;
  }
  .headerhome-aicheader-slide-content h1 {
    font-size: clamp(1.4rem, 2.5vw, 2.2rem);
  }
  .headerhome-aicheader-empowering-text::after {
    content: '';
    display: block;
    width: 80px;
    height: 5px;
    background-color: #e41e26;
    position: absolute;
    bottom: -10px;
    left: 18%;
    transform: translateX(-50%);
  }
  .headerhome-aicheader-red-boxes {
    width: 100%;
    position: relative;
    border-radius: 0;
    transform: none;
    left: 0;
    padding: 1rem 0; /* Adjust height further for small screens */
  }
  
  .headerhome-aicheader-feature {
    width: 50%;
  }
  
  .headerhome-aicheader-feature:nth-child(2n)::after {
    display: none;
  }
  
  .headerhome-aicheader-feature:nth-child(3n)::after {
    display: block;
  }
}

@media (max-width: 440px) {
  .headerhome-aicheader-slider {
    height: 70vh;
  }
  .headerhome-aicheader-slide-content h1 {
    font-size: clamp(1.2rem, 2vw, 1.8rem);
  }
  .headerhome-aicheader-red-boxes {
    flex-direction: column;
    padding: 0.5rem 0; /* Reduced height further for small screens */
  }

  .headerhome-aicheader-feature {
    width: 33.333%;
    flex: 0 0 auto;
    padding: 1rem 0.5rem;
  }

  .headerhome-aicheader-feature:nth-child(n+4) {
    border-top: 1px solid rgba(255, 255, 255, 0.3);
  }

  .headerhome-aicheader-feature:not(:nth-child(3n))::after {
    display: block;
  }

  .headerhome-aicheader-feature:nth-child(3n)::after {
    display: none;
  }

  .headerhome-aicheader-red-boxes::before {
    content: '';
    width: 100%;
    height: 1px;
    background-color: rgba(255, 255, 255, 0.3);
    position: absolute;
    top: 50%;
    left: 0;
  }
}

/* Additional media query for smaller laptops and zoomed displays */
@media (max-width: 1280px), (min-resolution: 120dpi) {
  .headerhome-aicheader-slide-content {
    right: 40%;
    width: 60%;
  }

  .headerhome-aicheader-slide-content h1 {
    font-size: clamp(1.5rem, 3vw, 2.5rem);
  }

  .headerhome-aicheader-empowering-text {
    font-size: clamp(0.9rem, 1.8vw, 1.1rem);
  }
}

/* For devices that support hover */
@media (hover: hover) {
  .headerhome-aicheader-nav ul li:hover .headerhome-aicheader-dropdown {
    display: block;
  }

  @media (max-height: 520){
  .headerhome-aicheader-slide-content {
    right: 40%;
    width: 60%;
  }
  .headerhome-aicheader-slider {
    height: 70vh;
  }
}
}