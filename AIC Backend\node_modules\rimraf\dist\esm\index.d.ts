import { RimrafAsyncOptions, RimrafSyncOptions } from './opt-arg.js';
export { assertRimrafOptions, isRimrafOptions, type RimrafAsyncOptions, type RimrafOptions, type RimrafSyncOptions, } from './opt-arg.js';
export declare const nativeSync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
export declare const native: ((path: string | string[], opt?: RimrafAsyncOptions) => Promise<boolean>) & {
    sync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
};
export declare const manualSync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
export declare const manual: ((path: string | string[], opt?: RimrafAsyncOptions) => Promise<boolean>) & {
    sync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
};
export declare const windowsSync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
export declare const windows: ((path: string | string[], opt?: RimrafAsyncOptions) => Promise<boolean>) & {
    sync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
};
export declare const posixSync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
export declare const posix: ((path: string | string[], opt?: RimrafAsyncOptions) => Promise<boolean>) & {
    sync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
};
export declare const moveRemoveSync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
export declare const moveRemove: ((path: string | string[], opt?: RimrafAsyncOptions) => Promise<boolean>) & {
    sync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
};
export declare const rimrafSync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
export declare const sync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
export declare const rimraf: ((path: string | string[], opt?: RimrafAsyncOptions) => Promise<boolean>) & {
    rimraf: (path: string | string[], opt?: RimrafAsyncOptions) => Promise<boolean>;
    sync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
    rimrafSync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
    manual: ((path: string | string[], opt?: RimrafAsyncOptions) => Promise<boolean>) & {
        sync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
    };
    manualSync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
    native: ((path: string | string[], opt?: RimrafAsyncOptions) => Promise<boolean>) & {
        sync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
    };
    nativeSync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
    posix: ((path: string | string[], opt?: RimrafAsyncOptions) => Promise<boolean>) & {
        sync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
    };
    posixSync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
    windows: ((path: string | string[], opt?: RimrafAsyncOptions) => Promise<boolean>) & {
        sync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
    };
    windowsSync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
    moveRemove: ((path: string | string[], opt?: RimrafAsyncOptions) => Promise<boolean>) & {
        sync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
    };
    moveRemoveSync: (path: string | string[], opt?: RimrafSyncOptions) => boolean;
};
//# sourceMappingURL=index.d.ts.map