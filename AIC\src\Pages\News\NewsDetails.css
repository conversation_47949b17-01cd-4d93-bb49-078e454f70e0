/* General styles */
body {
  font-family: 'Arial', sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f4f4f4;
}

.news-details-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  display: flex;
  gap: 60px;
}

/* Main content area */
.news-details-content {
  flex: 1;
  max-width: 800px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 30px;
}

.news-title {
  font-size: 36px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  border-bottom: 2px solid #e41e26;
  padding-bottom: 10px;
}

.news-category {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.news-image {
  width: 100%;
  max-height: 200px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 30px;
}

.news-body {
  font-size: 16px;
  line-height: 1.8;
  color: #333;
  margin-bottom: 30px;
}

.news-body img {
  max-width: 100%;
  height: auto;
  margin: 20px 0;
  border-radius: 4px;
}

/* Gallery section */
.gallery-section {
  margin-top: 40px;
  margin-bottom: 40px;
}

.gallery-section h3 {
  font-size: 24px;
  color: #333;
  margin-bottom: 20px;
}

.gallery-slider {
  position: relative;
  width: 100%;
  height: 400px;
  background-color: #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
}

.gallery-image-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.gallery-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.gallery-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(228, 30, 38, 0.7);
  color: #fff;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.3s;
  z-index: 10;
}

.gallery-arrow:hover {
  background: rgba(228, 30, 38, 0.9);
}

.left-arrow {
  left: 10px;
}

.right-arrow {
  right: 10px;
}

.gallery-caption {
  text-align: center;
  margin-top: 10px;
  color: #666;
  font-style: italic;
}

/* PDF Section */
.pdf-files {
  background-color: #f9f9f9;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  margin-top: 30px;
}

.pdf-files h3 {
  color: #333;
  margin-bottom: 15px;
}

.pdf-buttons-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.pdf-buttons {
  display: flex;
  gap: 15px;
  align-items: center;
}

.pdf-button {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-radius: 5px;
  text-decoration: none;
  font-weight: bold;
  transition: background-color 0.3s, color 0.3s;
}

.pdf-button.title {
  background-color: #e41e26;
  color: #fff;
  cursor: default;
}

.pdf-button.view {
  background-color: #f0f0f0;
  color: #333;
}

.pdf-button.view:hover {
  background-color: #e0e0e0;
}

.pdf-button svg {
  margin-right: 8px;
}

/* Tags */
.news-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 30px;
}

.tag {
  display: inline-block;
  background-color: #f0f0f0;
  color: #e41e26;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  text-decoration: none;
  transition: background-color 0.3s, color 0.3s;
}

.tag:hover {
  background-color: #e41e26;
  color: #fff;
}

/* Navigation */
.news-navigation {
  display: flex;
  justify-content: space-between;
  margin-top: 40px;
}

.news-nav-link {
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 16px;
  text-decoration: none;
  background-color: #e41e26;
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  transition: background-color 0.3s, transform 0.2s;
  cursor: pointer;
}

.news-nav-link:hover {
  background-color: #b81821;
  transform: translateY(-2px);
}

.news-nav-link svg {
  transition: transform 0.3s;
}

.prev svg {
  margin-right: 10px;
}

.next svg {
  margin-left: 10px;
}

.news-nav-link.prev:hover svg {
  transform: translateX(-5px);
}

.news-nav-link.next:hover svg {
  transform: translateX(5px);
}

/* Sidebar */
.news-sidebar {
  width: 340px;
  flex-shrink: 0;
  position: sticky;
  top: 20px;
  align-self: flex-start;
  max-height: calc(100vh - 40px);
  overflow-y: auto;
}

/* Project Carousel */
.project-carousel {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 30px;
}

.project-carousel h2 {
  font-size: 24px;
  color: #333;
  margin-bottom: 20px;
  border-bottom: 2px solid #e41e26;
  padding-bottom: 10px;
}

.project-card {
  margin-bottom: 20px;
}

.project-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 15px;
}

.project-card-content h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 10px;
}

.project-card-content p {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.read-more {
  display: inline-block;
  padding: 8px 15px;
  background-color: #e41e26;
  color: #fff;
  text-decoration: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
  transition: background-color 0.3s ease;
}

.read-more:hover {
  background-color: #b81821;
}

.project-navigation {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}

.project-nav-button {
  background: none;
  border: none;
  cursor: pointer;
  color: #e41e26;
  transition: color 0.3s;
}

.project-nav-button:hover {
  color: #b81821;
}

/* Responsive styles */
@media (max-width: 1024px) {
  .news-details-container {
    flex-direction: column;
  }

  .news-sidebar {
    width: 100%;
    max-width: 600px;
    margin: 40px auto 0;
    position: static;
    max-height: none;
    overflow-y: visible;
  }

  .project-carousel,
  .other-news {
    position: static;
  }
}

@media (max-width: 768px) {
  .news-details-container {
    padding: 20px;
  }

  .news-title {
    font-size: 28px;
  }

  .news-image {
    max-height: 550px;
  }

  .news-body {
    font-size: 15px;
  }

  .news-navigation {
    flex-direction: column;
    gap: 20px;
  }

  .news-nav-link {
    width: 100%;
    justify-content: center;
  }

  .gallery-slider {
    height: 300px;
  }

  .pdf-buttons {
    flex-direction: column;
  }

  .project-image {
    height: 180px;
  }

  .project-card-content h3 {
    font-size: 18px;
  }

  .project-card-content p {
    font-size: 13px;
  }
}