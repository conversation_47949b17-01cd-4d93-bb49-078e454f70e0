import React from 'react';
import { useLanguage } from '../../components/languageUtils';
import { motion, useAnimation } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { useNavigate } from 'react-router-dom';

const InstitutionalRelationships = () => {
  const { language } = useLanguage();
  const navigate = useNavigate();
  const controls = useAnimation();
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  React.useEffect(() => {
    if (inView) {
      controls.start('visible');
    }
  }, [controls, inView]);

  const translations = {
    en: {
      title: "RELATIONSHIP WITH OTHER PUBLIC SECTOR INSTITUTIONS",
      description: "The Albanian Investment Corporation fosters strong partnerships with key public sector institutions to ensure alignment with national development goals. By collaborating with government bodies, local municipalities, and regulatory agencies, we streamline the investment process and create a conducive environment for both domestic and international investors. These relationships enable us to work efficiently within Albania's regulatory framework, ensuring that our projects meet the highest standards of transparency and sustainability. Together, we are building a solid foundation for long-term economic growth and prosperity.",
      viewMore: "View Details",
      relationships: [
        {
          title: "RELATIONSHIP WITH LINE MINISTRIES",
          excerpt: "Coordination with line ministries to support sustainable economic development and attract investments."
        },
        {
          title: "RELATIONSHIP WITH AIDA",
          excerpt: "Collaboration with the Albanian Investment Development Agency to foster strategic investments."
        },
        {
          title: "RELATIONSHIP WITH THE ALBANIAN DEVELOPMENT FUND (ADF)",
          excerpt: "Joint projects to strengthen infrastructure and drive economic development across regions."
        },
        {
          title: "RELATIONSHIP WITH PROPERTY TREATMENT AGENCY",
          excerpt: "Strategic use of public property for economic development and long-term investments."
        },
        {
          title: "RELATIONSHIP WITH STATE CADASTRE AGENCY",
          excerpt: "Ensuring accurate administration and registration of public property for development."
        },
        {
          title: "RELATIONSHIP WITH TERRITORY DEVELOPMENT AGENCY",
          excerpt: "Sustainable and harmonized development of the territory aligned with planning goals."
        },
        {
          title: "RELATIONSHIP WITH NATIONAL TERRITORY PLANNING AGENCY",
          excerpt: "Ensuring compliance with territorial planning policies and instruments."
        },
        {
          title: "RELATIONSHIP WITH STATE AUTHORITY FOR GEOSPATIAL INFORMATION",
          excerpt: "Utilizing geospatial data for identification, planning, and development of property."
        },
        {
          title: "RELATIONSHIP WITH TIRANA MUNICIPALITY",
          excerpt: "Promoting economic and urban development of the capital through strategic projects."
        }
      ]
    },
    sq: {
      title: "MARRËDHËNIA ME INSTITUCIONET E TJERA TË SEKTORIT PUBLIK",
      description: "Korporata Shqiptare e Investimeve kultivon partneritete të qëndrueshme me institucionet kryesore të sektorit publik, duke siguruar përputhshmëri të plotë me objektivat kombëtare të zhvillimit. Përmes bashkëpunimit të ngushtë me organet qeveritare, bashkitë lokale dhe agjencitë rregullatore, ne lehtësojmë procesin e investimeve dhe krijojmë një mjedis të favorshëm për investitorët vendas dhe të huaj. Këto marrëdhënie na mundësojnë të operojmë me efikasitet brenda kuadrit rregullator të Shqipërisë, duke siguruar që projektet tona të përmbushin standardet më të larta të transparencës dhe qëndrueshmërisë. Së bashku, po ndërtojmë themelet për një rritje të qëndrueshme ekonomike dhe një prosperitet afatgjatë.",
      viewMore: "Shiko detajet",
      relationships: [
        {
          title: "MARRËDHËNIA ME MINISTRITË E LINJËS",
          excerpt: "Koordinim me ministritë e linjës për mbështetjen e zhvillimit ekonomik dhe tërheqjen e investimeve."
        },
        {
          title: "MARRËDHËNIA ME AIDA-n",
          excerpt: "Bashkëpunim me Agjencinë Shqiptare të Zhvillimit të Investimeve për nxitjen e investimeve strategjike."
        },
        {
          title: "MARRËDHËNIA ME FONDIN SHQIPTAR TË ZHVILLIMIT (FSHZH)",
          excerpt: "Projekte të përbashkëta për forcimin e infrastrukturës dhe nxitjen e zhvillimit ekonomik në rajone."
        },
        {
          title: "MARRËDHËNIA ME AGJENCINË E TRAJTIMIT TË PRONAVE",
          excerpt: "Përdorimi strategjik i pronës publike për zhvillim ekonomik dhe investime afatgjata."
        },
        {
          title: "MARRËDHËNIA ME AGJENCINË SHTETËRORE TË KADASTRËS",
          excerpt: "Sigurimi i administrimit dhe regjistrimit të saktë të pronës publike për zhvillim."
        },
        {
          title: "MARRËDHËNIA ME AGJENCINË E ZHVILLIMIT TË TERRITORIT",
          excerpt: "Zhvillim i qëndrueshëm dhe i harmonizuar i territorit në përputhje me qëllimet e planifikimit."
        },
        {
          title: "MARRËDHËNIA ME AGJENCINË KOMBËTARE TË PLANIFIKIMIT TË TERRITORIT",
          excerpt: "Sigurimi i përputhshmërisë me politikat dhe instrumentet e planifikimit territorial."
        },
        {
          title: "MARRËDHËNIA ME AUTORITETIN SHTETËROR PËR INFORMACIONIN GJEOHAPËSINOR",
          excerpt: "Përdorimi i të dhënave gjeohapësinore për identifikimin, planifikimin dhe zhvillimin e pronës."
        },
        {
          title: "MARRËDHËNIA ME BASHKINË TIRANË",
          excerpt: "Nxitja e zhvillimit ekonomik dhe urban të kryeqytetit përmes projekteve strategjike."
        }
      ]
    }
  };

  const t = translations[language];

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { 
        duration: 0.5,
        when: "beforeChildren",
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4 }
    }
  };

  const handleNavigate = () => {
    const path = language === 'en' ? '/en/networking' : '/sq/bashkepunetoret';
    navigate(path);
  };

  return (
    <div className="bg-gray-50 py-16 px-4 sm:px-6 lg:px-8" ref={ref}>
      <motion.div 
        className="max-w-7xl mx-auto"
        initial="hidden"
        animate={controls}
        variants={containerVariants}
      >
        {/* Header Section */}
        <motion.div 
          className="mb-16"
          variants={itemVariants}
        >
          <div className="bg-gradient-to-r from-red-700 to-red-500 rounded-2xl shadow-xl overflow-hidden">
            <div className="px-6 py-12 sm:px-12 sm:py-16 text-white text-center">
              <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 tracking-tight">
                {t.title}
              </h2>
              <p className="text-base sm:text-lg md:text-xl leading-relaxed max-w-4xl mx-auto">
                {t.description}
              </p>
            </div>
          </div>
        </motion.div>

        {/* Grid Layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {t.relationships.map((relationship, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-white rounded-xl shadow-md overflow-hidden transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
              onClick={handleNavigate}
              style={{ cursor: 'pointer' }}
            >
              <div className="p-6">
                <div className="h-2 w-24 bg-red-600 mb-4 rounded-full"></div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3 line-clamp-2">{relationship.title}</h3>
                <p className="text-gray-600 mb-4 line-clamp-3">{relationship.excerpt}</p>
                <div className="flex justify-end">
                  <span className="text-red-600 font-medium flex items-center">
                    {t.viewMore}
                    <svg 
                      xmlns="http://www.w3.org/2000/svg" 
                      className="h-5 w-5 ml-1" 
                      viewBox="0 0 20 20" 
                      fill="currentColor"
                    >
                      <path 
                        fillRule="evenodd" 
                        d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" 
                        clipRule="evenodd" 
                      />
                    </svg>
                  </span>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default InstitutionalRelationships;