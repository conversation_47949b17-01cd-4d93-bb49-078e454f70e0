/* WhyChooseAIC.css */

.centered-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 40px 20px;
    box-sizing: border-box;
  }
  
  .whychooseaic-container {
    display: flex;
    justify-content: space-between;
    gap: 40px;
  }
  
  .whychooseaic-content {
    flex: 1;
  }
  
  .whychooseaic-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .red-block {
    width: 40px;
    height: 5px;
    background-color: #e41e26;
    margin-right: 10px;
  }
  
  .whychooseaic-subtitle {
    font-size: 1.25rem;
    font-weight: bold;
    color: #333;
    text-transform: uppercase;
  }
  
  .whychooseaic-title {
    font-size: 2.25rem;
    font-weight: bold;
    color: #e41e26;
    margin-top: 10px;
    margin-bottom: 30px;
  }
  
  .whychooseaic-section {
    margin-bottom: 40px;
    background-color: #f8f8f8;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
  }
  
  .whychooseaic-section:hover {
    transform: translateY(-5px);
  }
  
  .section-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #fff;
    padding: 15px 20px;
    background-color: #e41e26;
    margin: 0;
  }
  
  .section-content {
    padding: 20px;
  }
  
  .section-content p {
    margin-bottom: 15px;
    line-height: 1.6;
  }
  
  .section-content h4 {
    color: #e41e26;
    margin-top: 20px;
    margin-bottom: 10px;
  }
  
  .section-content ul, .section-content ol {
    padding-left: 20px;
    margin-bottom: 15px;
  }
  
  .section-content li {
    margin-bottom: 10px;
  }
  
  .whychooseaic-sidebar {
    width: 300px;
    flex-shrink: 0;
  }
  
  .whychooseaic-nav {
    background-color: #f8f8f8;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
  
  .sidebar-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 1.5rem;
    text-transform: uppercase;
  }
  
  .whychooseaic-links {
    list-style-type: none;
    padding: 0;
  }
  
  .whychooseaic-link {
    display: block;
    padding: 1rem 1.25rem;
    margin-bottom: 0.75rem;
    background-color: #fff;
    border-radius: 0.5rem;
    transition: all 0.3s ease-in-out;
    font-size: 1rem;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    border: 1px solid #e0e0e0;
  }
  
  .whychooseaic-link:hover {
    background-color: #f0f0f0;
    transform: translateY(-2px);
  }
  
  .whychooseaic-link.active {
    background-color: #e41e26;
    color: white;
    box-shadow: 0 4px 6px rgba(228, 30, 38, 0.2);
  }
  
  @media (max-width: 1024px) {
    .whychooseaic-container {
      flex-direction: column;
    }
  
    .whychooseaic-sidebar {
      width: 100%;
    }
  }
  
  @media (max-width: 768px) {
    .whychooseaic-title {
      font-size: 1.75rem;
    }
  
    .section-title {
      font-size: 1.25rem;
    }
  
    .whychooseaic-link {
      font-size: 0.9rem;
      padding: 0.75rem 1rem;
    }
  }