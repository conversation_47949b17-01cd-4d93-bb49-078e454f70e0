.explore-our-section-wrapper {
  background-color: #f5f5f5;
  width: 100%;
  padding: 40px 0;
  position: relative;
  overflow: hidden;
}

.explore-our-section {
  max-width: 1300px;
  margin: 0 auto;
  padding: 0 30px;
  position: relative;
  z-index: 1;
}

.background-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.background-image {
  position: absolute;
  bottom: 0;
  width: 25%;
  height: auto;
  object-fit: cover;
  transition: filter 0.5s ease, opacity 0.5s ease;
  opacity: 0.7;
}

.background-image.left {
  left: 0;
  transform: translateX(-50%);
}

.background-image.right {
  right: 0;
  transform: translateX(50%) scaleX(-1);
}

.explore-our-section-wrapper:hover .background-image {
  animation: enhancedShine 6s infinite;
}

@keyframes enhancedShine {
  0%, 100% {
    filter: brightness(1.2) drop-shadow(0 0 15px rgba(221, 30, 38, 0.6));
    opacity: 0.2;
  }
  25% {
    filter: brightness(1.2) drop-shadow(0 0 25px rgba(221, 30, 38, 0.8));
    opacity: 0.4;
  }
  50% {
    filter: brightness(1.3) drop-shadow(0 0 35px rgba(221, 30, 38, 1));
    opacity: 0.6;
  }
  75% {
    filter: brightness(1.2) drop-shadow(0 0 25px rgba(221, 30, 38, 0.8));
    opacity: 0.4;
  }
}

.exploreoursections-header {
  text-align: center;
  margin-bottom: 50px;
}

.exploreoursections-subtitle {
  color: #555;
  font-size: 20px;
  font-weight: 500;
  letter-spacing: 1.2px;
  margin-bottom: 10px;
  text-transform: uppercase;
}

.exploreoursections-main-title {
  color: #c81a21;
  font-size: 38px;
  font-weight: 700;
  text-transform: uppercase;
  margin: 0;
  letter-spacing: 2px;
}

.sector-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
  margin-bottom: 30px;
  position: relative;
}

.sector-card {
  position: relative;
  height: 320px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 2px solid #c81a21;
  border-radius: 5px;
}

.sector-card:hover {
  transform: scale(1.05);
  box-shadow: 0 0 20px rgba(221, 30, 38, 0.7);
}

.sector-content {
  position: relative;
  height: 100%;
}

.sector-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.sector-title-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.15);
}

.sector-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
  overflow: visible;
}

.sector-card:hover .sector-overlay {
  opacity: 1;
}

.overlay-content {
  max-width: 70%;
}

.sector-overlay h3 {
  font-size: 24px;
  margin-bottom: 10px;
  text-align: left;
}

.sector-overlay p {
  font-size: 14px;
  text-align: left;
}

.hover-image-container {
  position: absolute;
  right: 0;
  bottom: 0;
  transform: translate(50%, 50%);
  pointer-events: none;
}

.hover-image {
  display: block;
  max-width: none;
  max-height: none;
  width: auto;
  height: auto;
  transition: filter 0.3s ease;
}

.sector-card:hover .hover-image {
  filter: drop-shadow(0 0 10px rgba(221, 30, 38, 0.2));
}

.bottom-left-image-container {
  position: absolute;
  left: 0;
  bottom: 0;
  transform: translate(-50%, 50%);
  pointer-events: none;
}

.bottom-left-image {
  display: block;
  max-width: none;
  max-height: none;
  width: auto;
  height: auto;
}

.sector-title {
  position: absolute;
  bottom: 20px;
  left: 20px;
  color: white;
  font-size: 24px;
  z-index: 1;
}

.view-projects-btn-container {
  text-align: center;
}

.view-projects-btn {
  display: inline-block;
  margin: 30px auto 0;
  padding: 10px 30px;
  background-color: #e41e26;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  text-transform: uppercase;
  border-radius: 5px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.view-projects-btn:hover {
  background-color: #c81a21;
  box-shadow: 0 0 15px rgba(221, 30, 38, 0.5);
}

/* Media query for screens 768px and below */
@media (max-width: 768px) {
  .sector-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .sector-card {
    height: 200px;
  }

  .background-image {
    width: 37.5%;
  }

  .background-image.left {
    transform: translateX(-33%);
  }

  .background-image.right {
    transform: translateX(33%) scaleX(-1);
  }

  .hover-image-container {
    display: none;
  }

  .sector-overlay {
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 10px;
  }

  .overlay-content {
    max-width: 90%;
  }

  .sector-overlay h3 {
    font-size: 18px;
    text-align: center;
    margin-bottom: 5px;
  }

  .sector-overlay p {
    font-size: 12px;
    text-align: center;
  }

  .sector-title {
    font-size: 20px;
    bottom: 10px;
    left: 10px;
  }

  .exploreoursections-main-title {
    font-size: 28px;
  }

  .exploreoursections-subtitle {
    font-size: 16px;
  }

  .view-projects-btn {
    padding: 8px 20px;
    font-size: 14px;
  }
}

/* For devices that support hover */
@media (hover: hover) {
  .sector-card:hover .sector-overlay {
    opacity: 1;
  }
}