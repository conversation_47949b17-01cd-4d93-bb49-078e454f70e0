import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../components/languageUtils';
import SEO from '../../components/SEO';
import { seoTitles } from '../../config/seoTitles';
import sanityClient from '../../sanity'; // Ensure this path is correct

const ByLawDocumentPage = () => {
  const { language } = useLanguage();
  const seoData = seoTitles.lawsRegulations[language];
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Fixed GROQ query - fetch all language fields and select in JavaScript
    const query = `
      *[_type == "lawpdfCategory"] | order(sortOrder asc) {
        _id,
        title_en,
        title_sq,
        isClickable,
        "subcategories": subcategories[]-> {
          _id,
          title_en,
          title_sq,
          isClickable,
          "tertiaryCategories": tertiaryCategories[]-> {
            _id,
            title_en,
            title_sq,
            isClickable,
            "documents": documents[]-> {
              _id,
              title_en,
              title_sq,
              "file_en_url": file_en.asset->url,
              "file_sq_url": file_sq.asset->url,
              sortOrder
            } | order(sortOrder asc)
          } | order(sortOrder asc),
          "documents": documents[]-> {
            _id,
            title_en,
            title_sq,
            "file_en_url": file_en.asset->url,
            "file_sq_url": file_sq.asset->url,
            sortOrder
          } | order(sortOrder asc)
        } | order(sortOrder asc),
        "documents": documents[]-> {
          _id,
          title_en,
          title_sq,
          "file_en_url": file_en.asset->url,
          "file_sq_url": file_sq.asset->url,
          sortOrder
        } | order(sortOrder asc)
      }
    `;

    sanityClient
      .fetch(query)
      .then((result) => {
        console.log('Fetched data:', result); // Debug log
        
        // Process the data to select language-specific fields
        const processedCategories = result?.map(category => ({
          ...category,
          title: language === 'sq' ? category.title_sq : category.title_en,
          subcategories: category.subcategories?.map(subcategory => ({
            ...subcategory,
            title: language === 'sq' ? subcategory.title_sq : subcategory.title_en,
            tertiaryCategories: subcategory.tertiaryCategories?.map(tertiary => ({
              ...tertiary,
              title: language === 'sq' ? tertiary.title_sq : tertiary.title_en,
              documents: tertiary.documents?.map(doc => ({
                ...doc,
                title: language === 'sq' ? doc.title_sq : doc.title_en,
                fileUrl: language === 'sq' ? doc.file_sq_url : doc.file_en_url
              })) || []
            })) || [],
            documents: subcategory.documents?.map(doc => ({
              ...doc,
              title: language === 'sq' ? doc.title_sq : doc.title_en,
              fileUrl: language === 'sq' ? doc.file_sq_url : doc.file_en_url
            })) || []
          })) || [],
          documents: category.documents?.map(doc => ({
            ...doc,
            title: language === 'sq' ? doc.title_sq : doc.title_en,
            fileUrl: language === 'sq' ? doc.file_sq_url : doc.file_en_url
          })) || []
        })) || [];

        setCategories(processedCategories);
        setLoading(false);
      })
      .catch((err) => {
        console.error("Error fetching documents:", err);
        setError(language === 'sq' 
          ? "Ndodhi një gabim gjatë ngarkimit të dokumenteve" 
          : "Failed to load documents");
        setLoading(false);
      });
  }, [language]); // Re-fetch when language changes

  // Handle loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-red-600"></div>
      </div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <div className="py-8 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-5xl mx-auto">
          <div className="bg-white p-6 rounded-lg shadow-sm text-center">
            <div className="text-xl text-red-600 mb-2">⚠️ {error}</div>
            <p className="text-gray-600">
              {language === 'sq' 
                ? "Ju lutemi provoni përsëri më vonë ose kontaktoni administratorin" 
                : "Please try again later or contact the administrator"}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Handle empty categories
  if (!categories || categories.length === 0) {
    return (
      <div className="py-8 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-5xl mx-auto">
          <div className="bg-white p-6 rounded-lg shadow-sm text-center">
            <p className="text-gray-600">
              {language === 'sq' 
                ? "Asnjë dokument nuk u gjet. Dokumentet do të shtohen së shpejti." 
                : "No documents found. Documents will be added soon."}
            </p>
          </div>
        </div>
      </div>
    );
  }
  
  // Render a document link
  const renderDocumentLink = (doc) => {
    if (!doc || !doc.fileUrl) {
      console.warn('Document missing or no fileUrl:', doc); // Debug log
      return null;
    }
    return (
      <li key={doc._id}>
        <a 
          href={doc.fileUrl} 
          className="text-blue-500 hover:underline" 
          target="_blank" 
          rel="noopener noreferrer"
        >
          {doc.title || (language === 'sq' ? 'Dokument pa titull' : 'Untitled document')}
        </a>
      </li>
    );
  };

  // Render tertiary category with its documents
  const renderTertiaryCategory = (tertiaryCategory) => {
    if (!tertiaryCategory) return null;
    
    return (
      <li key={tertiaryCategory._id}>
        <span className={tertiaryCategory.isClickable ? "text-blue-500" : ""}>
          {tertiaryCategory.title || (language === 'sq' ? 'Kategori pa titull' : 'Untitled category')}
        </span>
        {tertiaryCategory.documents && tertiaryCategory.documents.length > 0 ? (
          <ul className="ml-8 list-disc space-y-1 mt-1">
            {tertiaryCategory.documents.map(doc => renderDocumentLink(doc))}
          </ul>
        ) : null}
      </li>
    );
  };

  // Render subcategory with its tertiary categories and documents
  const renderSubcategory = (subcategory) => {
    if (!subcategory) return null;
    
    // Check if this subcategory has any content to display
    const hasDocuments = subcategory.documents && subcategory.documents.length > 0;
    const hasTertiaryCategories = subcategory.tertiaryCategories && subcategory.tertiaryCategories.length > 0;
    
    if (!hasDocuments && !hasTertiaryCategories) return null;
    
    return (
      <li key={subcategory._id}>
        <span className={subcategory.isClickable ? "text-blue-500" : ""}>
          {subcategory.title || (language === 'sq' ? 'Nënkategori pa titull' : 'Untitled subcategory')}
        </span>
        
        {/* Render direct documents in this subcategory */}
        {hasDocuments && (
          <ul className="ml-8 list-disc space-y-1 mt-1">
            {subcategory.documents.map(doc => renderDocumentLink(doc))}
          </ul>
        )}
        
        {/* Render tertiary categories */}
        {hasTertiaryCategories && (
          <ul className="ml-8 list-disc space-y-1 mt-1">
            {subcategory.tertiaryCategories.map(tertiary => renderTertiaryCategory(tertiary))}
          </ul>
        )}
      </li>
    );
  };

  // Check if we have any content at all
  const hasAnyContent = categories.some(category => {
    const hasDirectDocuments = category.documents && category.documents.length > 0;
    const hasSubcategories = category.subcategories && category.subcategories.length > 0;
    return hasDirectDocuments || hasSubcategories;
  });

  // If no content at all, show empty state
  if (!hasAnyContent) {
    return (
      <div className="py-8 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-5xl mx-auto">
          <div className="bg-white p-6 rounded-lg shadow-sm text-center">
            <p className="text-gray-600">
              {language === 'sq' 
                ? "Asnjë dokument nuk u gjet. Dokumentet do të shtohen së shpejti." 
                : "No documents found. Documents will be added soon."}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Main render function for the component
  return (
    <>
      <SEO
        customTitle={seoData.title}
        customDescription={seoData.description}
        language={language}
        ogType="website"
      />
      <div className="py-8 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="max-w-5xl mx-auto">
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <ul className="list-disc space-y-2 ml-5">
            {categories.map(category => {
              // Skip categories with no content
              const hasDirectDocs = category.documents && category.documents.length > 0;
              const hasSubcats = category.subcategories && category.subcategories.length > 0 && 
                category.subcategories.some(sub => 
                  (sub.documents && sub.documents.length > 0) || 
                  (sub.tertiaryCategories && sub.tertiaryCategories.length > 0)
                );
              
              if (!hasDirectDocs && !hasSubcats) return null;
              
              return (
                <li key={category._id}>
                  <span className={category.isClickable ? "text-blue-500" : ""}>
                    {category.title || (language === 'sq' ? 'Kategori pa titull' : 'Untitled category')}
                  </span>
                  
                  {/* Render direct documents in this category */}
                  {hasDirectDocs && (
                    <ul className="ml-8 list-disc space-y-1 mt-1">
                      {category.documents.map(doc => renderDocumentLink(doc))}
                    </ul>
                  )}
                  
                  {/* Render subcategories */}
                  {hasSubcats && (
                    <ul className="ml-8 list-disc space-y-1 mt-1">
                      {category.subcategories.map(subcategory => renderSubcategory(subcategory))}
                    </ul>
                  )}
                </li>
              );
            })}
          </ul>
        </div>
      </div>
    </div>
    </>
  );
};

export default ByLawDocumentPage;