import { defineField, defineType } from 'sanity'
import basePostSchema from './basePostSchema'

export const newsPost = defineType({
  name: 'newsPost',
  title: '<PERSON><PERSON><PERSON>/Post News',
  type: 'document',
  fields: [
    ...basePostSchema.fields,
    defineField({
      name: 'postType',
      title: 'Post Type',
      type: 'string',
      initialValue: 'newsPost',
      readOnly: true,
      hidden: true,
    }),
  ],
  preview: basePostSchema.preview,
})

export const eventsPost = defineType({
  name: 'eventsPost',
  title: '<PERSON><PERSON><PERSON>/Post Events',
  type: 'document',
  fields: [
    ...basePostSchema.fields,
    defineField({
      name: 'postType',
      title: 'Post Type',
      type: 'string',
      initialValue: 'eventsPost',
      readOnly: true,
      hidden: true,
    }),
    defineField({
      name: 'eventDate',
      title: 'Event Date',
      type: 'datetime',
    }),
  ],
  preview: basePostSchema.preview,
})

export const noticesPost = defineType({
  name: 'noticesPost',
  title: '<PERSON><PERSON><PERSON>/Post Notices',
  type: 'document',
  fields: [
    ...basePostSchema.fields,
    defineField({
      name: 'postType',
      title: 'Post Type',
      type: 'string',
      initialValue: 'noticesPost',
      readOnly: true,
      hidden: true,
    }),
  ],
  preview: basePostSchema.preview,
})

interface CareerPostSelection {
  jobTitle?: string;
  department?: string;
  media?: any;
}

export const careerPost = defineType({
  name: 'careerPost',
  title: 'Krijo Karriere/Post Career',
  type: 'document',
  fields: [
    ...basePostSchema.fields,
    defineField({
      name: 'postType',
      title: 'Post Type',
      type: 'string',
      initialValue: 'careerPost',
      readOnly: true,
      hidden: true,
    }),
    defineField({
      name: 'jobTitle',
      title: 'Job Title',
      type: 'string',
    }),
    defineField({
      name: 'department',
      title: 'Department',
      type: 'string',
    }),
    defineField({
      name: 'jobType',
      title: 'Job Type',
      type: 'string',
      options: {
        list: [
          { title: 'Full-time', value: 'full-time' },
          { title: 'Part-time', value: 'part-time' },
          { title: 'Contract', value: 'contract' },
          { title: 'Internship', value: 'internship' },
        ],
      },
    }),
    defineField({
      name: 'location',
      title: 'Location',
      type: 'string',
    }),
    defineField({
      name: 'salary',
      title: 'Salary Range',
      type: 'string',
    }),
    defineField({
      name: 'applicationDeadline',
      title: 'Application Deadline',
      type: 'date',
    }),
  ],
  preview: {
    select: {
      jobTitle: 'jobTitle',
      department: 'department',
      media: 'mainImage',
    },
    prepare(selection: CareerPostSelection) {
      const {jobTitle, department, media} = selection
      return {
        title: jobTitle || 'Untitled Job Posting',
        subtitle: department || 'No department specified',
        media: media
      }
    }
  },
})

export const postSchemas = { newsPost, eventsPost, noticesPost, careerPost }

export default postSchemas