// components/404.js
import React from "react";
import { useNavigate } from "react-router-dom";
import { useLanguage } from "./languageUtils";

const NotFoundPage = () => {
  const navigate = useNavigate();
  const { language } = useLanguage();

  const content = {
    en: {
      title: "404",
      subtitle: "Page Not Found",
      message: "Sorry, the page you are looking for does not exist.",
      buttonText: "Return Home",
    },
    sq: {
      title: "404",
      subtitle: "Faqja nuk u gjet",
      message: "Na vjen keq, faqja që kërkoni nuk ekziston.",
      buttonText: "Kthehu në Faqen Kryesore",
    },
  };

  const { title, subtitle, message, buttonText } = content[language] || content.en;

  return (
    <div className="min-h-screen bg-white flex flex-col items-center justify-center text-center px-4">
      <h1 className="text-9xl font-bold text-red-600">{title}</h1>
      <h2 className="text-3xl font-semibold text-black mt-4">{subtitle}</h2>
      <p className="text-lg text-gray-700 mt-2">{message}</p>
      <button
        onClick={() => navigate(`/${language}`)}
        className="mt-8 px-6 py-3 bg-red-600 text-white rounded-full hover:bg-red-700 transition duration-200"
      >
        {buttonText}
      </button>
    </div>
  );
};

export default NotFoundPage;
