import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../../components/languageUtils';
import SEO from '../../components/SEO';
import { seoTitles } from '../../config/seoTitles';
import sanityClient from '../../sanity';

const TeamMemberCard = ({ name, role, image, profileLink, isVacant }) => {
  const { language } = useLanguage();

  const ImageContent = () => (
    image ? (
      <img 
        loading="lazy" 
        src={image} 
        alt={name} 
        className="w-full h-full object-cover" 
      />
    ) : (
      <div className="w-full h-full bg-gray-300 flex items-center justify-center">
        👤
      </div>
    )
  );

  return (
    <div className="group relative rounded-3xl overflow-hidden w-72 h-[23rem] bg-white shadow-lg transition-all duration-300 ease-in-out mb-8 flex flex-col hover:bg-red-600 hover:shadow-xl hover:shadow-red-600/35 hover:-translate-y-2.5">
      <div className="p-8 flex-grow flex flex-col items-center text-center">
        <div className="w-40 h-40 rounded-full overflow-hidden mb-4 border-4 border-red-600 transition-all duration-300 ease-in-out group-hover:border-white">
          {profileLink ? (
            <Link to={profileLink} aria-label={`View ${name}'s profile`}>
              <ImageContent />
            </Link>
          ) : (
            <ImageContent />
          )}
        </div>
        <div className="w-full flex-1 flex flex-col">
          <h3 className="text-[1.55rem] font-bold text-black transition-colors duration-300 ease-in-out font-['Playfair_Display',_serif] mb-2 group-hover:text-white">
            {isVacant ? (language === 'en' ? 'Position Available' : 'Pozicion i Lirë') : name}
          </h3>
          <div className="flex-1 flex items-center justify-center">
            <p className="text-[1.1rem] text-gray-600 transition-colors duration-300 ease-in-out font-['Playfair_Display',_serif] font-medium group-hover:text-white">
              {role}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

// Helper function to safely extract role text for a specific language
const safeGetRoleText = (member, language, fallbackLanguage) => {
  // Initialize with empty string
  let roleText = "";
  
  try {
    // First try to get the requested language from role
    if (member.role && typeof member.role === 'object') {
      // Check if the requested language exists in role
      if (member.role[language] !== undefined) {
        roleText = member.role[language];
      }
      // If not found and we have a fallback language, try that
      else if (fallbackLanguage && member.role[fallbackLanguage] !== undefined) {
        roleText = member.role[fallbackLanguage];
        console.log(`Used fallback language for role on member '${member.name || "unnamed"}'`);
      }
    }
    
    // If still no result, try positionTitle
    if (!roleText && member.positionTitle && typeof member.positionTitle === 'object') {
      // Check if the requested language exists in positionTitle
      if (member.positionTitle[language] !== undefined) {
        roleText = member.positionTitle[language];
      }
      // If not found and we have a fallback language, try that
      else if (fallbackLanguage && member.positionTitle[fallbackLanguage] !== undefined) {
        roleText = member.positionTitle[fallbackLanguage];
        console.log(`Used fallback language for positionTitle on member '${member.name || "unnamed"}'`);
      }
    }
  } catch (error) {
    console.error(`Error getting role text for member:`, member, error);
  }
  
  return roleText;
};

const TeamMembersShowcase = () => {
  const { language } = useLanguage();
  const seoData = seoTitles.meetTheStaff[language];
  const [teamMembers, setTeamMembers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Determine fallback language
  const fallbackLanguage = language === 'en' ? 'sq' : 'en';

  useEffect(() => {
    // Sanity query to fetch all team members
    const query = `*[_type == "teamMember"] | order(position asc) {
      _id,
      name,
      "slug": slug.current,
      "image": image.asset->url,
      role,
      positionTitle,
      isVacant,
      position
    }`;

    sanityClient
      .fetch(query)
      .then((data) => {
        // Log the raw data for debugging
        console.log("Raw team members data:", JSON.stringify(data, null, 2));
        setTeamMembers(data);
        setLoading(false);
      })
      .catch((error) => {
        console.error("Error fetching team members:", error);
        setError(error.message || "Failed to load team members");
        setLoading(false);
      });
  }, []);

  // Transform Sanity data to format needed by the component
  const formattedTeamMembers = React.useMemo(() => {
    try {
      if (!teamMembers || !Array.isArray(teamMembers)) {
        console.error("Team members is not an array:", teamMembers);
        return [];
      }
      
      return teamMembers.map((member, index) => {
        if (!member) {
          console.error(`Null/undefined team member at index ${index}`);
          return {
            name: `Unknown Member ${index}`,
            role: "",
            image: null,
            profileLink: null,
            isVacant: false
          };
        }
        
        // Get role text with fallback language option
        const roleText = safeGetRoleText(member, language, fallbackLanguage);
        
        // Create the correct URL path based on language
        let profileLink = null;
        if (!member.isVacant && member.slug) {
          profileLink = language === 'en' 
            ? `/en/team/meet-the-staff/${member.slug}`
            : `/sq/ekipi/njihuni-me-stafin/${member.slug}`;
        }

        // Log problematic members for debugging
        if (!roleText) {
          console.warn(`Member with missing role text:`, {
            index,
            id: member._id,
            name: member.name,
            role: member.role,
            positionTitle: member.positionTitle
          });
        }

        return {
          name: member.name || `Member ${index}`,
          role: roleText,
          image: member.image || null,
          profileLink: profileLink,
          isVacant: !!member.isVacant
        };
      });
    } catch (err) {
      console.error("Error formatting team members:", err);
      return [];
    }
  }, [teamMembers, language, fallbackLanguage]);

  const content = {
    en: {
      subheading: "OUR TEAM",
      heading: "MEET OUR DEDICATED TEAM",
      loading: "Loading team members...",
      error: "Failed to load team members. Please try again later."
    },
    sq: {
      subheading: "EKIPI YNË",
      heading: "NJIHUNI ME EKIPIN TONË TË PËRKUSHTUAR",
      loading: "Duke ngarkuar anëtarët e ekipit...",
      error: "Dështoi në ngarkimin e anëtarëve të ekipit. Ju lutemi provoni përsëri më vonë."
    }
  };

  return (
    <>
      <SEO
        customTitle={seoData.title}
        customDescription={seoData.description}
        language={language}
        ogType="website"
      />
      <div className="mx-auto bg-gray-100 pt-[50px]" style={{
        backgroundImage: "url('/public/Asset 3.png')",
        backgroundSize: "300px 300px",
        backgroundPosition: "top",
        backgroundRepeat: "no-repeat",
      backgroundOrigin: "content-box" 
    }}>
      <h2 className="text-center text-xl font-semibold mb-3 text-gray-600 font-['Playfair_Display',_serif]">
        {content[language].subheading}
      </h2>
      <h1 className="text-center text-4xl font-bold text-red-600 mb-16 font-['Playfair_Display',_serif]">
        {content[language].heading}
      </h1>
      
      {loading ? (
        <div className="text-center py-10">{content[language].loading}</div>
      ) : error ? (
        <div className="text-center py-10 text-red-600">{content[language].error}</div>
      ) : (
        <div className="flex justify-center">
          <div className="flex flex-wrap gap-8 justify-center mb-5 mt-2.5 px-4 max-w-[1200px] mx-auto
                        lg:gap-4 
                        md:gap-4 md:max-w-full
                        sm:gap-4 sm:max-w-full">
            {formattedTeamMembers.map((member, index) => (
              <TeamMemberCard 
                key={`team-member-${index}-${member.isVacant ? 'vacant' : (member.name || 'unnamed')}`} 
                {...member} 
              />
            ))}
          </div>
        </div>
      )}
    </div>
    </>
  );
};

export default TeamMembersShowcase;