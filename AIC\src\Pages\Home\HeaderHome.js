import React, { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Clock, Mail, MapPin, Linkedin, Instagram, Facebook, ChevronDown, Twitter } from 'lucide-react';
import LanguageSwitcher, { GlobalLanguageContext } from '../../components/LanguageSwitcher';

const HeaderHome = () => {
  const { t, i18n } = useTranslation(['homeHeader', 'common']);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 1024);
  const [openMobileDropdown, setOpenMobileDropdown] = useState(null);
  const [openMobileSubDropdown, setOpenMobileSubDropdown] = useState(null);
  const { globalLanguage } = useContext(GlobalLanguageContext);

  useEffect(() => {
    if (globalLanguage !== i18n.language) {
      i18n.changeLanguage(globalLanguage);
    }
  }, [globalLanguage, i18n]);

  const menuItems = [
    {
      title: t('homeHeader:menu.aboutUs'),
      submenu: [
        { title: t('homeHeader:submenus.aboutUs.visionMission'), link: `/${i18n.language}/${t('homeHeader:routes.aboutUs')}/${t('homeHeader:routes.visionMission')}` },
        { title: t('homeHeader:submenus.aboutUs.whyChooseAIC'), link: `/${i18n.language}/${t('homeHeader:routes.aboutUs')}/${t('homeHeader:routes.whyChooseAIC')}` },
        { title: t('homeHeader:submenus.aboutUs.consultancy'), link: `/${i18n.language}/${t('homeHeader:routes.aboutUs')}/${t('homeHeader:routes.consultancy')}` },
        { title: t('homeHeader:submenus.aboutUs.lawsRegulations'), link: `/${i18n.language}/${t('homeHeader:routes.aboutUs')}/${t('homeHeader:routes.lawsRegulations')}` },
      ],
    },
    { title: t('homeHeader:menu.news'), link: `/${i18n.language}/${t('homeHeader:routes.news')}` },
    {
      title: t('homeHeader:menu.projects'),
      link: `/${i18n.language}/${t('homeHeader:routes.projects')}`,
      submenu: [
        { 
          title: t('homeHeader:submenus.projects.strategicProjects'), 
          link: `/${i18n.language}/${t('homeHeader:routes.projects')}/${t('homeHeader:routes.strategicProjects')}` 
        },
        { 
          title: t('homeHeader:submenus.projects.revitalization'), 
          link: `/${i18n.language}/${t('homeHeader:routes.projects')}/${t('homeHeader:routes.revitalization')}` 
        },
        { 
          title: t('homeHeader:submenus.projects.realEstate'), 
          link: `/${i18n.language}/${t('homeHeader:routes.projects')}/${t('homeHeader:routes.realEstate')}`,
          submenu: [
            { 
              title: t('homeHeader:submenus.projects.realEstateSubcategories.coastal'), 
              link: `/${i18n.language}/${t('homeHeader:routes.projects')}/${t('homeHeader:routes.realEstate')}/${t('homeHeader:routes.coastalAreas')}` 
            },
            { 
              title: t('homeHeader:submenus.projects.realEstateSubcategories.downtown'), 
              link: `/${i18n.language}/${t('homeHeader:routes.projects')}/${t('homeHeader:routes.realEstate')}/${t('homeHeader:routes.downtownAreas')}` 
            },
            { 
              title: t('homeHeader:submenus.projects.realEstateSubcategories.publicService'), 
              link: `/${i18n.language}/${t('homeHeader:routes.projects')}/${t('homeHeader:routes.realEstate')}/${t('homeHeader:routes.publicServiceComplex')}` 
            },
          ]
        },
      ]
    },
    {
      title: t('homeHeader:menu.calls'),
      submenu: [
        { title: t('homeHeader:submenus.calls.openCalls'), link: `/${i18n.language}/${t('homeHeader:routes.calls')}/${t('homeHeader:routes.openCalls')}` },
        { title: t('homeHeader:submenus.calls.upcomingCalls'), link: `/${i18n.language}/${t('homeHeader:routes.calls')}/${t('homeHeader:routes.upcomingCalls')}` },
        { title: t('homeHeader:submenus.calls.ongoing'), link: `/${i18n.language}/${t('homeHeader:routes.calls')}/${t('homeHeader:routes.ongoing')}` },
        { title: t('homeHeader:submenus.calls.closedCalls'), link: `/${i18n.language}/${t('homeHeader:routes.calls')}/${t('homeHeader:routes.closedCalls')}` },
      ],
    },
    {
      title: t('homeHeader:menu.team'),
      submenu: [
        { title: t('homeHeader:submenus.team.institutionalStructure'), link: `/${i18n.language}/${t('homeHeader:routes.team')}/${t('homeHeader:routes.institutionalStructure')}` },
        { title: t('homeHeader:submenus.team.meetTheStaff'), link: `/${i18n.language}/${t('homeHeader:routes.team')}/${t('homeHeader:routes.meetTheStaff')}` },
        { title: t('homeHeader:submenus.team.joinUs'), link: `/${i18n.language}/${t('homeHeader:routes.team')}/${t('homeHeader:routes.joinUs')}` },
      ],
    },
    // Updated networking item to be clickable with no submenu
    {
      title: t('homeHeader:menu.networking'),
      link: `/${i18n.language}/${t('homeHeader:routes.networking')}`
    },
    { 
      title: t('homeHeader:menu.transparency'), 
      link: `/${i18n.language}/${t('homeHeader:routes.transparency')}` 
    },
    { 
      title: t('homeHeader:menu.openData'), 
      link: `/${i18n.language}/${t('homeHeader:routes.openData')}` 
    },
    { 
      title: t('homeHeader:menu.map'), 
      link: `/${i18n.language}/${t('homeHeader:routes.map')}` 
    },
    { 
      title: t('homeHeader:menu.contact'), 
      link: `/${i18n.language}/${t('homeHeader:routes.contact')}` 
    },
  ];

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
    document.body.style.overflow = isDropdownOpen ? 'auto' : 'hidden';
  };

  const toggleMobileDropdown = (index, e) => {
    e.stopPropagation();
    setOpenMobileDropdown(openMobileDropdown === index ? null : index);
    setOpenMobileSubDropdown(null); // Reset sub-dropdown when toggling a new main dropdown
  };

  const toggleMobileSubDropdown = (index, e) => {
    e.stopPropagation();
    setOpenMobileSubDropdown(openMobileSubDropdown === index ? null : index);
  };

  useEffect(() => {
    const handleResize = () => {
      const isMobileView = window.innerWidth <= 1024;
      setIsMobile(isMobileView);

      if (!isMobileView) {
        setIsDropdownOpen(false);
        document.body.style.overflow = 'auto';
      }
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      document.body.style.overflow = 'auto';
    };
  }, []);

  return (
    <header className="absolute w-full text-white font-rubik z-50">
      {/* Top Info Bar */}
      <div className="hidden lg:flex justify-end items-center py-2 px-5 text-sm">
        <div className="flex gap-5">
          <span className="flex items-center"><Clock className="w-4 h-4 mr-1" /> {t('homeHeader:topInfo.workHours')}</span>
          <span className="flex items-center"><Mail className="w-4 h-4 mr-1" /> {t('homeHeader:topInfo.email')}</span>
          <span className="flex items-center"><MapPin className="w-4 h-4 mr-1" /> {t('homeHeader:topInfo.address')}</span>
        </div>
        <Link 
          to={`/${i18n.language === 'sq' ? 'sq/kontakt' : 'en/contact'}`}
          className="bg-red-600 text-white px-4 py-1 rounded-lg ml-5 hover:bg-red-700 transition-colors duration-300"
        >
          {t('homeHeader:common.investNow')}
        </Link>
      </div>

      {/* Main Menu */}
      <div className="flex justify-between items-center px-5 relative">
        <div className="py-2">
          <Link to="/">
            <img src="../AIClogo.png" alt="Albanian Investment Corporation Logo" className="h-24 w-72 object-contain" />
          </Link>
        </div>

        {isMobile && (
          <button 
            className="lg:hidden flex flex-col justify-center items-center w-8 h-8 z-[101]" 
            onClick={toggleDropdown}
            aria-label={isDropdownOpen ? "Close menu" : "Open menu"}
          >
            {/* Animated hamburger icon */}
            <div className="relative w-6 h-6">
              {/* Top line */}
              <span 
                className={`absolute h-0.5 w-6 transform transition-all duration-300 ease-in-out ${
                  isDropdownOpen ? 'bg-red-600 top-3 rotate-45' : 'bg-white top-1'
                }`}
              ></span>
              
              {/* Middle line */}
              <span 
                className={`absolute h-0.5 w-6 transform transition-all duration-300 ease-in-out top-3 ${
                  isDropdownOpen ? 'bg-red-600 opacity-0' : 'bg-white opacity-100'
                }`}
              ></span>
              
              {/* Bottom line */}
              <span 
                className={`absolute h-0.5 w-6 transform transition-all duration-300 ease-in-out ${
                  isDropdownOpen ? 'bg-red-600 top-3 -rotate-45' : 'bg-white top-5 rotate-0'
                }`}
              ></span>
            </div>
          </button>
        )}

        <nav className={`hidden lg:flex ${isDropdownOpen ? 'active' : ''}`}>
          <ul className="flex">
            {menuItems.map((item, index) => (
              <li key={index} className="relative group">
                {item.link ? (
                  <Link to={item.link} className="px-4 py-2 block hover:bg-white/10 transition duration-300">
                    {item.title}
                  </Link>
                ) : (
                  <span className="px-4 py-2 block cursor-default hover:bg-white/10 transition duration-300">
                    {item.title}
                  </span>
                )}
                {item.submenu && (
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 bg-red-600 rounded-md hidden group-hover:block w-56 shadow-lg z-50">
                    <ul className="py-2">
                      {item.submenu.map((subItem, subIndex) => (
                        <li key={subIndex} className="px-2 relative group/sub">
                          {subItem.link ? (
                            <Link 
                              to={subItem.link} 
                              className="px-2 py-2 text-left hover:bg-white hover:text-red-600 transition duration-300 rounded flex justify-between items-center"
                            >
                              <span>{subItem.title}</span>
                              {subItem.submenu && <ChevronDown className="w-4 h-4 rotate-270" />}
                            </Link>
                          ) : subItem.displayOnly ? (
                            <span className="block px-2 py-2 text-left cursor-default hover:bg-white/5 transition duration-300 rounded opacity-90">
                              {subItem.title}
                            </span>
                          ) : (
                            <span className="block px-2 py-2 text-left cursor-default hover:bg-white hover:text-red-600 transition duration-300 rounded">
                              {subItem.title}
                            </span>
                          )}
                          
                          {/* Third level submenu */}
                          {subItem.submenu && (
                            <div className="absolute left-full top-0 bg-red-600 rounded-md hidden group-hover/sub:block w-56 shadow-lg">
                              <ul className="py-2">
                                {subItem.submenu.map((subSubItem, subSubIndex) => (
                                  <li key={subSubIndex} className="px-2">
                                    {subSubItem.displayOnly ? (
                                      <span className="block px-2 py-2 text-left cursor-default hover:bg-white/5 transition duration-300 rounded opacity-90">
                                        {subSubItem.title}
                                      </span>
                                    ) : (
                                      <Link
                                        to={subSubItem.link}
                                        className="block px-2 py-2 text-left hover:bg-white hover:text-red-600 transition duration-300 rounded"
                                      >
                                        {subSubItem.title}
                                      </Link>
                                    )}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </li>
            ))}
          </ul>
        </nav>

        {/* Language Switcher and Social Media Icons */}
        <div className={`hidden lg:flex items-center gap-4 mr-24`}>
          <LanguageSwitcher />
          <div className="flex gap-2">
            <a href="https://www.linkedin.com/company/the-albanian-investment-corporation/" 
               aria-label="LinkedIn" 
               target="_blank" 
               rel="noopener noreferrer"
               className="hover:text-red-600 transition-colors duration-300">
              <Linkedin className="w-4 h-4" />
            </a>
            <a href="https://www.instagram.com/albanianinvestmentcorporation/" 
               aria-label="Instagram" 
               target="_blank" 
               rel="noopener noreferrer"
               className="hover:text-red-600 transition-colors duration-300">
              <Instagram className="w-4 h-4" />
            </a>
            <a href="https://www.facebook.com/AlbanianInvestmentCorporation" 
               aria-label="Facebook" 
               target="_blank" 
               rel="noopener noreferrer"
               className="hover:text-red-600 transition-colors duration-300">
              <Facebook className="w-4 h-4" />
            </a>
            <a href="https://x.com/albania_aic/" 
               aria-label="Twitter" 
               target="_blank" 
               rel="noopener noreferrer"
               className="hover:text-red-600 transition-colors duration-300">
              <Twitter className="w-4 h-4" />
            </a>
          </div>
        </div>
      </div>
      <div className="border-b border-white/20 mx-5"></div>

      {/* Mobile Dropdown Menu with smooth transition */}
      {isMobile && (
        <div 
          className={`fixed inset-0 bg-white z-[100] transform transition-transform duration-500 ease-in-out ${
            isDropdownOpen ? 'translate-x-0' : 'translate-x-full'
          }`}
        >
          <div className="pt-20 px-5 h-full overflow-y-auto">
            <nav>
              <ul className="space-y-1">
                {menuItems.map((item, index) => (
                  <li key={index} className="border-b border-red-100 last:border-b-0">
                    <div className="flex justify-between items-center">
                      {item.link ? (
                        <Link 
                          to={item.link} 
                          className="text-red-600 font-bold py-3 block w-full hover:bg-red-50 rounded transition duration-300" 
                          onClick={toggleDropdown}
                        >
                          {item.title}
                        </Link>
                      ) : (
                        <span className="text-red-600 font-bold py-3 block w-full hover:bg-red-50 rounded transition duration-300">
                          {item.title}
                        </span>
                      )}
                      {item.submenu && (
                        <button
                          className="p-3"
                          onClick={(e) => toggleMobileDropdown(index, e)}
                          aria-label={openMobileDropdown === index ? "Close submenu" : "Open submenu"}
                        >
                          <ChevronDown
                            className={`w-6 h-6 text-red-600 transform transition-transform duration-300 ${
                              openMobileDropdown === index ? 'rotate-180' : ''
                            }`}
                          />
                        </button>
                      )}
                    </div>
                    {item.submenu && (
                      <div 
                        className={`overflow-hidden transition-all duration-300 ease-in-out ${
                          openMobileDropdown === index 
                            ? 'max-h-96 opacity-100' 
                            : 'max-h-0 opacity-0'
                        }`}
                      >
                        <ul className="pl-5 py-2 bg-red-50 rounded-lg mb-2">
                          {item.submenu.map((subItem, subIndex) => (
                            <li key={subIndex}>
                              <div className="flex justify-between items-center">
                                {subItem.link ? (
                                  <Link 
                                    to={subItem.link} 
                                    onClick={toggleDropdown} 
                                    className="text-red-600 py-2 block hover:bg-red-100 px-3 rounded transition duration-300 flex-grow"
                                  >
                                    {subItem.title}
                                  </Link>
                                ) : subItem.displayOnly ? (
                                  <span className="text-red-600/70 py-2 block px-3 rounded transition duration-300 flex-grow cursor-default">
                                    {subItem.title}
                                  </span>
                                ) : (
                                  <span className="text-red-600 py-2 block hover:bg-red-100 px-3 rounded transition duration-300 flex-grow">
                                    {subItem.title}
                                  </span>
                                )}
                                {subItem.submenu && (
                                  <button
                                    className="p-2"
                                    onClick={(e) => toggleMobileSubDropdown(subIndex, e)}
                                    aria-label={openMobileSubDropdown === subIndex ? "Close submenu" : "Open submenu"}
                                  >
                                    <ChevronDown
                                      className={`w-5 h-5 text-red-600 transform transition-transform duration-300 ${
                                        openMobileSubDropdown === subIndex ? 'rotate-180' : ''
                                      }`}
                                    />
                                  </button>
                                )}
                              </div>
                              
                              {/* Third level submenu for mobile */}
                              {subItem.submenu && (
                                <div 
                                  className={`overflow-hidden transition-all duration-300 ease-in-out ${
                                    openMobileSubDropdown === subIndex 
                                      ? 'max-h-60 opacity-100' 
                                      : 'max-h-0 opacity-0'
                                  }`}
                                >
                                  <ul className="pl-5 py-1 bg-red-100 rounded-lg my-1">
                                    {subItem.submenu.map((subSubItem, subSubIndex) => (
                                      <li key={subSubIndex}>
                                        {subSubItem.displayOnly ? (
                                          <span className="text-red-600/70 py-2 block px-3 rounded transition duration-300 cursor-default">
                                            {subSubItem.title}
                                          </span>
                                        ) : (
                                          <Link 
                                            to={subSubItem.link} 
                                            onClick={toggleDropdown} 
                                            className="text-red-600 py-2 block hover:bg-red-200 px-3 rounded transition duration-300"
                                          >
                                            {subSubItem.title}
                                          </Link>
                                        )}
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </li>
                ))}
              </ul>
            </nav>
            <div className="border-t border-red-300 my-5"></div>
            
            <div className="flex justify-between items-center mb-6">
              <LanguageSwitcher />
              <div className="flex gap-4">
                <a href="https://www.facebook.com/AlbanianInvestmentCorporation" 
                   aria-label="Facebook" 
                   target="_blank" 
                   rel="noopener noreferrer"
                   className="hover:text-red-700 transition-colors duration-300">
                  <Facebook className="w-6 h-6 text-red-600" />
                </a>
                <a href="https://www.instagram.com/albanianinvestmentcorporation/" 
                   aria-label="Instagram" 
                   target="_blank" 
                   rel="noopener noreferrer"
                   className="hover:text-red-700 transition-colors duration-300">
                  <Instagram className="w-6 h-6 text-red-600" />
                </a>
                <a href="https://www.linkedin.com/company/the-albanian-investment-corporation/" 
                   aria-label="LinkedIn" 
                   target="_blank" 
                   rel="noopener noreferrer"
                   className="hover:text-red-700 transition-colors duration-300">
                  <Linkedin className="w-6 h-6 text-red-600" />
                </a>
                <a href="https://x.com/albania_aic/" 
                   aria-label="Twitter" 
                   target="_blank" 
                   rel="noopener noreferrer"
                   className="hover:text-red-700 transition-colors duration-300">
                  <Twitter className="w-6 h-6 text-red-600" />
                </a>
              </div>
            </div>
            
            <Link 
              to={`/${i18n.language === 'sq' ? 'sq/kontakt' : 'en/contact'}`}
              className="bg-red-600 text-white px-4 py-3 rounded font-bold hover:bg-red-700 transition duration-300 block text-center"
              onClick={toggleDropdown}
            >
              {t('homeHeader:common.investNow')}
            </Link>
          </div>
        </div>
      )}
    </header>
  );
};

export default HeaderHome;