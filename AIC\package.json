{"name": "my-app", "version": "0.1.0", "private": true, "dependencies": {"@emailjs/browser": "^4.4.1", "@portabletext/react": "^3.2.1", "@sanity/block-content-to-react": "^3.0.0", "@sanity/client": "^6.21.3", "buffer": "^6.0.3", "crypto-browserify": "^3.12.0", "framer-motion": "^11.5.6", "i18next": "^23.15.2", "i18next-browser-languagedetector": "^8.0.0", "i18next-http-backend": "^2.6.2", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "leaflet.markercluster": "^1.5.3", "lucide-react": "^0.436.0", "nodemailer": "^6.9.15", "process": "^0.11.10", "react": "^18.2.0", "react-dom": "^18.2.0", "react-ga4": "^2.1.0", "react-helmet-async": "^2.0.5", "react-i18next": "^15.0.2", "react-intersection-observer": "^9.13.1", "react-router-dom": "^6.26.1", "react-scripts": "^5.0.1", "react-swipeable": "^7.0.1", "swiper": "^11.1.12", "web-vitals": "^4.2.3"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-transform-class-properties": "^7.22.5", "@babel/preset-env": "^7.26.0", "@babel/preset-react": "^7.25.9", "@craco/craco": "^5.9.0", "autoprefixer": "^10.4.20", "babel-loader": "^9.2.1", "crypto-js": "^4.2.0", "eslint": "^8.57.0", "eslint-webpack-plugin": "^4.2.0", "javascript-obfuscator": "^4.1.1", "postcss": "^8.4.45", "react-app-rewired": "^2.2.1", "tailwindcss": "^3.4.11", "terser-webpack-plugin": "^5.3.10", "webpack-obfuscator": "^3.5.1"}, "browser": {"process": "process/browser.js"}}