// src/App.js
import React from 'react';
import { useLanguage } from '../../components/languageUtils';
import SEO from '../../components/SEO';
import { seoTitles } from '../../config/seoTitles';
import './Contact.css';
import ContactForm from './ContactForm';


function Contact() {
  const { language } = useLanguage();
  const seoData = seoTitles.contact[language];

  return (
    <>
      <SEO
        customTitle={seoData.title}
        customDescription={seoData.description}
        language={language}
        ogType="website"
      />
      <div className="App">
        <ContactForm/>
      </div>
    </>
  );
}

export default Contact;