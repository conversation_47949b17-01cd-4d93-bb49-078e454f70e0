import React, { useEffect } from 'react';

const GoogleAnalyticsScript = () => {
  useEffect(() => {

    const script1 = document.createElement('script');
    script1.async = true;
    script1.src = "https://www.googletagmanager.com/gtag/js?id=G-VDJHDXTWZE";
    
    const script2 = document.createElement('script');
    script2.id = 'ga-inline-script'; 
    script2.innerHTML = `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-VDJHDXTWZE', {
        send_page_view: true,
        debug_mode: true
      });
      console.log('Google Analytics initialized');
    `;
    
    const existingScript1 = document.querySelector('script[src="https://www.googletagmanager.com/gtag/js?id=G-VDJHDXTWZE"]');
    const existingScript2 = document.getElementById('ga-inline-script');
    
    // Instead of: document.querySelector('script:not([src]):contains("window.dataLayer")')
    /* 
    let existingScript2 = null;
    document.querySelectorAll('script:not([src])').forEach(script => {
      if (script.textContent.includes('window.dataLayer')) {
        existingScript2 = script;
      }
    });
    */
    
    if (existingScript1) existingScript1.remove();
    if (existingScript2) existingScript2.remove();
    
    // Add scripts to head
    document.head.appendChild(script1);
    document.head.appendChild(script2);
    
    setTimeout(() => {
      if (window.gtag) {
        window.gtag('event', 'test_event', {
          'event_category': 'test',
          'event_label': 'initialization'
        });
        console.log('Test event sent to Google Analytics');
      }
    }, 2000);
    
    return () => {

      if (script1.parentNode) script1.parentNode.removeChild(script1);
      if (script2.parentNode) script2.parentNode.removeChild(script2);
    };
  }, []);
  
  return null;
};

export default GoogleAnalyticsScript;