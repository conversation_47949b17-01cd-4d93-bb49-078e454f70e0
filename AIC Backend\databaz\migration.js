// migration.js - Run this in a Sanity migration script
const { createClient } = require('@sanity/client');

// ════════════════════ IMPORTANT: ADD YOUR WRITE TOKEN HERE ════════════════════
// Generate a token with WRITE permissions from https://manage.sanity.io
// Go to your project → API → Tokens → Add API token → Editor role (or higher)
const SANITY_TOKEN = 'sk1gFQg7yYmfNzjxfOUBiaUVStITzKBBsA8RQBs6oNSI5PiPzLIr4D4WTf4MXCL1CgRVglImzu23CW5wZwBqMwPzzWH8GfTgv5m0JIFZ6LkS40RAUfExqPq2E4YVi3R4tr0KHviJI27ycwHqXW4tIs3a4JkT8YLe92XfXmwOYhpaREfySFF3'; // Replace with your actual token
// ═══════════════════════════════════════════════════════════════════════════════

// Get a client for performing migrations
const client = createClient({
  projectId: '1ag8k8v1',
  dataset: 'production2',
  apiVersion: '2021-10-21',
  token: SANITY_TOKEN, // ← This is required for write operations
  useCdn: false
});

// Utility function to create an empty localized field
function createLocalizedField(enValue, sqValue = null) {
  return {
    en: enValue,
    sq: sqValue
  };
}

// Verify token has write access before proceeding
async function checkWriteAccess() {
  try {
    // Try a simple read operation first to check connection
    console.log("Testing Sanity connection...");
    await client.fetch('count(*[_type == "property"])');
    
    // Now test permissions with a harmless document creation and deletion
    console.log("Testing write permissions...");
    
    // Create a temporary document
    const testDoc = {
      _id: 'migration-test-document',
      _type: 'test',
      note: 'This is a test document to check write permissions'
    };
    
    // Try to create it
    await client.createOrReplace(testDoc);
    
    // If we got here, we have write access - clean up the test doc
    await client.delete('migration-test-document');
    
    console.log("✓ Authentication successful! You have write access.");
    return true;
  } catch (err) {
    console.error("✗ Authentication failed!");
    console.error(`Error: ${err.message}`);
    
    if (err.statusCode === 403) {
      console.error('\n╔════════════════════════════════════════════════════════════════════╗');
      console.error('║ PERMISSION DENIED: Your token does not have write permissions        ║');
      console.error('║                                                                      ║');
      console.error('║ How to fix this:                                                     ║');
      console.error('║ 1. Go to https://manage.sanity.io                                    ║');
      console.error('║ 2. Select your project: "1ag8k8v1"                                   ║');
      console.error('║ 3. Go to API → Tokens                                                ║');
      console.error('║ 4. Create a new token with "Editor" or "Admin" role                  ║');
      console.error('║ 5. Copy the token and paste it in this script as SANITY_TOKEN        ║');
      console.error('╚════════════════════════════════════════════════════════════════════╝');
    } else if (!SANITY_TOKEN || SANITY_TOKEN === 'YOUR_WRITE_TOKEN_HERE') {
      console.error('\n╔════════════════════════════════════════════════════════════════════╗');
      console.error('║ ERROR: You need to add your Sanity token to this script              ║');
      console.error('║                                                                      ║');
      console.error('║ Replace "YOUR_WRITE_TOKEN_HERE" with an actual token from            ║');
      console.error('║ https://manage.sanity.io → API → Tokens → Add API token              ║');
      console.error('╚════════════════════════════════════════════════════════════════════╝');
    }
    
    return false;
  }
}

// Main migration function
async function migrateProperties() {
  try {
    // First check if we have proper write access
    const hasAccess = await checkWriteAccess();
    if (!hasAccess) {
      console.log("Migration aborted due to permission issues.");
      return;
    }
    
    // Get all properties from the old schema
    const properties = await client.fetch('*[_type == "property"]');
    
    console.log(`Found ${properties.length} properties to migrate`);
    
    // Process each property
    const migrationPromises = properties.map(async (property) => {
      console.log(`Migrating property: ${property.title || property._id}`);
      
      // Build the new property document with localized fields
      const newProperty = {
        // Keep the same document ID
        _id: property._id,
        _type: 'property',
        
        // Migrate text fields to localized structure
        title: createLocalizedField(property.title || ''),
        location: createLocalizedField(property.location || ''),
        function: createLocalizedField(property.function || ''),
        status: createLocalizedField(property.status || ''),
        value: createLocalizedField(property.value || ''),
        partnerships: createLocalizedField(property.partnerships || ''),
        developer: createLocalizedField(property.developer || ''),
        manager: createLocalizedField(property.manager || ''),
        beneficiary: createLocalizedField(property.beneficiary || ''),
        designer: createLocalizedField(property.designer || ''),
        
        // Keep geopoint as is
        geopoint: property.geopoint || null,
        
        // Migrate distance fields to localized
        distanceCityCenter: createLocalizedField(property.distanceCityCenter || ''),
        distanceTIA: createLocalizedField(property.distanceTIA || ''),
        
        // Keep measurement fields as is
        areaSize: property.areaSize || 'TBD',
        constructionArea: property.constructionArea || 'TBD',
        businessSpace: property.businessSpace || 'TBD',
        hotelSpace: property.hotelSpace || 'TBD',
        officeSpace: property.officeSpace || 'TBD',
        residentialSpace: property.residentialSpace || 'TBD',
        
        // Migrate images to localized structure
        projectImage: createLocalizedField(property.projectImage || null),
        mapImage: createLocalizedField(property.mapImage || null),
        
        // Rename permit image fields
        developmentPermit: createLocalizedField(property.developmentPermitImage || null),
        constructionPermit: createLocalizedField(property.constructionPermitImage || null),
        
        // Initialize empty PDF field
        projectPDF: createLocalizedField(null),
        
        // Keep gallery array as is
        gallery: property.gallery || [],
        
        // Migrate description to localized structure
        description: createLocalizedField(property.description || [])
      };
      
      try {
        // Create a transaction to handle the migration
        const transaction = client.transaction();
        
        // First patch (optional) - mark fields as deprecated
        transaction.patch(property._id, {
          set: {
            '_migratedToLocalized': true,
            '_migratedAt': new Date().toISOString()
          }
        });
        
        // Then create the new document structure
        transaction.createOrReplace(newProperty);
        
        // Commit the transaction
        const result = await transaction.commit();
        console.log(`✅ Successfully migrated property: ${property.title || property._id}`);
        return result;
      } catch (err) {
        console.error(`❌ Failed to migrate property: ${property.title || property._id}`, err);
        return null;
      }
    });
    
    // Wait for all migrations to complete
    const results = await Promise.all(migrationPromises);
    
    // Report results
    console.log(`Migration completed. Migrated ${results.filter(Boolean).length} properties.`);
    if (results.some(r => !r)) {
      console.error(`Failed to migrate ${results.filter(r => !r).length} properties.`);
    }
  } catch (err) {
    console.error('Migration failed:', err);
  }
}

// Run the migration
migrateProperties().catch(console.error);