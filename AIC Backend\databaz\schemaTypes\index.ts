import { type SchemaTypeDefinition } from 'sanity'
import blockContent from './blockContent'
import tag from './tag'
import bio from './bio'
import * as lawspdfs from './lawspdf'  // Import all named exports
import projectsPost from './projectsPost'
import projectBlockContent from './projectBlockContent'
import { newsPost, eventsPost, careerPost, noticesPost } from './postSchemas'
import documentCategory from './documentCategory'
import properties from './property'
import popup from "./welcomePopup"
import openData from './openData'

export const schemaTypes = [
  newsPost,
  eventsPost,
  careerPost,
  noticesPost,
  blockContent,
  tag,
  bio,
  // Import the renamed schemas from lawspdf
  lawspdfs.lawpdfCategorySchema,
  lawspdfs.lawpdfSubcategorySchema,
  lawspdfs.lawpdfTertiaryCategorySchema,
  lawspdfs.lawpdfItemSchema,
  projectsPost,
  projectBlockContent,
  documentCategory,
  properties,
  popup,
  openData,
]

export const schema: { types: SchemaTypeDefinition[] } = {
  types: schemaTypes,
}