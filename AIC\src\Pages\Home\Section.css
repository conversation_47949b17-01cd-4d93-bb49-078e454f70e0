.investment-section-container {
  max-width: 1340px;
  margin: 0 auto;
  padding: 0 15px;
  min-height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.investment-section {
  width: 100%;
  display: flex;
  padding: 2rem;
  font-family: Arial, sans-serif;
}

.content-column {
  flex: 1;
  padding-right: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: left;
  min-height: 300px; /* Increased height */
}

.red-shape {
  width: 50px;
  height: 4px;
  background-color: #ff0000;
  margin-bottom: 10px;
}

.content-column h2 {
  color: #000000;
  font-size: clamp(0.9rem, 1vw, 1rem);
  margin-bottom: 0.5rem;
  font-weight: normal;
}

.executive-name h1 a {
  text-decoration: none; /* Removes the underline */
  color: #ff0000; /* Reverts the color to red */
}

.executive-name h1 a:hover {
  color: #ff0000; /* Ensures the color stays red when hovered */
}

.content-column h1 {
  color: #ff0000;
  font-size: clamp(1.5rem, 2.5vw, 2rem);
  margin-bottom: 1rem;
  font-weight: bold;
}

.content-column p {
  color: #666666;
  font-size: clamp(0.9rem, 1vw, 1rem);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.content-column p:first-of-type {
  font-weight: bold;
  color: #888888; /* Bold gray color for the first paragraph */
}

.view-projects {
  background-color: #ff0000;
  color: white;
  border: none;
  padding: 0.7rem 1.5rem;
  cursor: pointer;
  font-size: clamp(0.9rem, 1vw, 1rem);
  font-weight: bold;
  border-radius: 50px;
  align-self: flex-start;
}

.image-column {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  padding-left: 2rem;
}

.image-composition {
  position: relative;
  width: 90%;
  padding-bottom: 140%;
  margin: 0 auto;
}

.video-container {
  position: absolute;
  top: 0;
  left: -50px;
  width: 400px;
  height: 584px;
  border-radius: 50px;
  overflow: hidden;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.city-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: left center;
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #ffffffb3;
  border: none;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 24px;
  color: #fff;
}

.year-badge {
  position: absolute;
  top: 95px;
  right: 10px;
  background-color: #ff0000;
  color: white;
  padding: 15px 40px;
  font-size: 1rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  z-index: 20;
  border: 2px solid white;
  box-sizing: border-box;
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 15px;
}

.year-badge::before {
  content: "";
  position: absolute;
  top: -6px;
  left: -6px;
  right: -6px;
  bottom: -6px;
  border: 4px solid #ff0000;
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 15px;
  pointer-events: none;
}

.year-badge span:first-child {
  font-size: 0.9rem;
  display: block;
}

.year {
  font-size: 1.8rem;
  font-weight: 800;
}

.executive-container {
  position: absolute;
  bottom: 50px;
  right: -100px;
  width: 70%;
  height: 70%;
  border-radius: 70px;
  overflow: hidden;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  z-index: 10;
}

.executive-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: left center;
}

@font-face {
  font-family: "Corinthiago";
  src: url("/fonts/Corinthiago.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

.executive-name {
  position: absolute;
  bottom: 50px;
  left: -100px;
  width: 100%;
  text-align: left;
  z-index: 15;
}

.executive-name h1 {
  margin-left: 100px;
  padding-bottom: 50px;
  font-size: 35px;
  color: #ff0000;
  font-family: "Corinthiago", cursive;
}

.executive-name h3 {
  margin-left: 100px;
  margin-top: -50px;
  margin-bottom: 60px;
  font-size: 15px;
  color: #333;
}

.video-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.video-modal-content {
  position: relative;
  width: 80%;
  max-width: 800px;
}

.close-modal {
  position: absolute;
  top: -40px;
  right: 0;
  background: none;
  border: none;
  color: white;
  font-size: 30px;
  cursor: pointer;
}

/* Default styles (for larger screens) */
.investment-section {
  flex-direction: row;
  padding: 2rem;
}

/* Styles for 1171px and above */
@media (min-width: 1171px) {
  .image-composition {
    width: 90%;
    padding-bottom: 140%;
  }

  .video-container {
    width: 400px;
    height: 584px;
    left: -50px;
    top: 0;
  }

  .executive-container {
    bottom: 50px;
    right: -100px;
    width: 70%;
    height: 70%;
  }

  .executive-name {
    bottom: 50px;
    left: -100px;
  }

  .year-badge {
    top: 95px;
    right: 10px;
  }
}

/* Styles for 768px to 1170px */
@media (max-width: 1170px) {
  .investment-section {
    flex-direction: column;
    padding: 1.5rem;
  }

  .content-column {
    padding-right: 0;
    margin-bottom: 2rem;
  }

  .image-column {
    padding-left: 0;
  }

  .image-composition {
    width: 80%;
    padding-bottom: 120%;
  }

  .video-container {
    width: 300px;
    height: 538px;
    left: 0;
  }

  .executive-container {
    right: -60px;
    bottom: 40px;
  }

  .executive-name {
    left: -30px;
    bottom: 100px;
  }

  .year-badge {
    top: auto;
    bottom: 640px;
    right: 60px;
  }
}

/* Styles for 601px to 767px */
@media (min-width: 601px) and (max-width: 767px) {
  .investment-section {
    flex-direction: column;
    padding: 1.5rem;
  }

  .image-composition {
    width: 90%;
    padding-bottom: 130%;
  }

  .video-container {
    width: 280px;
    height: 500px;
    left: 0;
  }

  .executive-container {
    right: -40px;
    bottom: 60px;
    width: 75%;
    height: 75%;
  }

  .executive-name {
    left: -20px;
    bottom: 120px;
  }

  .year-badge {
    bottom: 600px;
    right: 40px;
  }
}

/* Mobile styles (600px and below) */
@media (max-width: 600px) {
  .content-column {
    flex: 1;
    padding-right: 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    min-height: 300px; /* Increased height */
    align-items: center;
    padding-left: 15px;
    padding-right: 15px;
  }
  .view-projects {
    background-color: #ff0000;
    color: white;
    border: none;
    padding: 0.7rem 1.5rem;
    cursor: pointer;
    font-size: clamp(0.9rem, 1vw, 1rem);
    font-weight: bold;
    border-radius: 50px;
    align-self: center;
  }

  .investment-section {
    padding: 0.5rem;
  }

  .image-composition {
    padding-bottom: 0;
    height: auto;
    margin-bottom: 2rem;
  }

  .video-container {
    position: relative;
    width: 120%; /* Increased from 100% */
    height: 0;
    padding-bottom: 175%; /* Increased from 146% */
    border-radius: 25px;
    margin-bottom: 2rem;
    overflow: hidden;
    left: -10%; /* Compensate for increased width */
  }

  .city-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: left center;
  }

  .executive-container {
    position: relative;
    bottom: auto;
    right: 10%; /* Shift slightly to the right */
    width: 120%; /* Increased from 100% */
    padding-bottom: 120%; /* Increased from 100% */
    border-radius: 25px;
    margin-bottom: 1rem;
    overflow: hidden;
  }

  .executive-image {
    position: absolute;
    top: 0;
    left: 60px;
    width: 100%;
    height: 100%;
    object-fit: flex;
    object-position: left;
    transform: scale(1.7); /* Adjust this value as needed */
  }

  .year-badge {
    top: auto;
    bottom: calc(45% + 10px);
    right: -10%; /* Adjusted for new container width */
    padding: 10px 15px;
  }

  .executive-name {
    position: relative;
    bottom: auto;
    left: 23%; /* Align with the left edge of the screen */
    width: 77%; /* Match container width */
    margin-top: 1rem;
  }

  .executive-name h1 {
    font-size: 30px;
    padding: 0;
    margin: 0;
  }

  .executive-name h3 {
    font-size: 16px;
    padding: 0;
    margin: 0;
  }
}

/* Smaller mobile styles */
@media (max-width: 320px) {
  .year-badge {
    padding: 8px 12px;
  }

  .year {
    font-size: 1.2rem;
  }

  .executive-name h1 {
    font-size: 2rem;
  }

  .executive-name h3 {
    font-size: 1rem;
  }
}
@media (min-width: 768px) and (max-width: 1024px) {
  .section-photo-1 {
    width: 75% !important;
  }
  .section-photo-2 {
    width: 70% !important;
    left: 55% !important;
  }
}
