import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../components/languageUtils';
import SEO from '../../components/SEO';
import { seoTitles } from '../../config/seoTitles';
import { Eye, Download, FileText, FileSpreadsheet, ChevronRight, ExternalLink } from 'lucide-react';
import { PortableText } from '@portabletext/react';
import sanityClient from '../../sanity';

const translations = {
  en: {
    title: "Programs and Transparency",
    viewDocument: "View Document",
    downloadDocument: "Download",
    noDocumentsAvailable: "No documents are currently available.",
    documentsAvailable: "documents available",
    error: "Error loading documents. Please try again later.",
    allCategories: "All Categories",
    visitPage: "Visit Page",
    untitledDocument: "Untitled Document"
  },
  sq: {
    title: "Programi i Transparencës",
    viewDocument: "Shiko <PERSON>kumentin",
    downloadDocument: "Shkarko",
    noDocumentsAvailable: "Aktualisht nuk ka dokumente të disponueshme.",
    documentsAvailable: "dokumente në dispozicion",
    error: "Gabim në ngarkimin e dokumenteve. Ju lutemi provoni përsëri më vonë.",
    allCategories: "Të gjitha Kategoritë",
    visitPage: "Vizito Faqen",
    untitledDocument: "Dokument Pa Titull"
  }
};

const DocumentCategoriesViewer = () => {
  const { i18n } = useTranslation();
  const { language } = useLanguage();
  const seoData = seoTitles.transparency[language];
  const [categories, setCategories] = useState([]);
  const [activeCategory, setActiveCategory] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const t = (key) => translations[i18n.language]?.[key] || translations['en'][key];

  useEffect(() => {
    sanityClient
      .fetch(`*[_type == "documentCategory"] | order(order asc) {
        _id,
        customTitle_en,
        customTitle_sq,
        contactInfo_en,
        contactInfo_sq,
        enableLink,
        categoryUrl,
        order,
        "documents": documents[] | order(order asc) {
          customTitle_en,
          customTitle_sq,
          order,
          enableLink,
          documentUrl,
          "fileUrl_en": file_en.asset->url,
          "fileUrl_sq": file_sq.asset->url,
          "fileName_en": file_en.asset->originalFilename,
          "fileName_sq": file_sq.asset->originalFilename,
          "extension_en": file_en.asset->extension,
          "extension_sq": file_sq.asset->extension
        }
      }`)
      .then((result) => {
        const processedResult = result.map(category => ({
          ...category,
          documents: category.documents || []
        }));
        setCategories(processedResult);
        setActiveCategory(processedResult[0]?._id);
        setLoading(false);
      })
      .catch((err) => {
        console.error("Error fetching categories:", err);
        setError(t('error'));
        setLoading(false);
      });
  }, [i18n.language]);

  const getCurrentLanguageData = (item) => {
    if (!item) return { title: '', fileUrl: '', fileName: '', extension: '', contactInfo: null };
    
    const lang = i18n.language;
    
    // Ensure we prioritize custom titles and provide a fallback when both are empty
    const customTitle = lang === 'sq' 
      ? (item.customTitle_sq || item.customTitle_en || t('untitledDocument'))
      : (item.customTitle_en || item.customTitle_sq || t('untitledDocument'));
    
    return {
      title: customTitle,
      fileUrl: lang === 'sq' ? item.fileUrl_sq || item.fileUrl_en : item.fileUrl_en || item.fileUrl_sq,
      fileName: customTitle, // Use the custom title for downloads too instead of original filename
      extension: lang === 'sq' ? item.extension_sq || item.extension_en : item.extension_en || item.extension_sq,
      contactInfo: lang === 'sq' ? item.contactInfo_sq || item.contactInfo_en : item.contactInfo_en || item.contactInfo_sq
    };
  };

  const getFileIcon = (extension) => {
    if (!extension) return <FileText className="text-gray-500 w-4 h-4 sm:w-5 sm:h-5" />;
    switch(extension.toLowerCase()) {
      case 'pdf': return <FileText className="text-red-500 w-4 h-4 sm:w-5 sm:h-5" />;
      case 'xls':
      case 'xlsx': return <FileSpreadsheet className="text-green-500 w-4 h-4 sm:w-5 sm:h-5" />;
      case 'ppt':
      case 'pptx': return <FileSpreadsheet className="text-orange-500 w-4 h-4 sm:w-5 sm:h-5" />;
      default: return <FileText className="text-gray-500 w-4 h-4 sm:w-5 sm:h-5" />;
    }
  };

  const handleDownload = async (e, fileUrl, fileName) => {
    e.preventDefault();
    e.stopPropagation();
    if (!fileUrl) return;
    
    try {
      const response = await fetch(fileUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // Use custom title for the download name, with appropriate extension
      const extension = fileUrl.split('.').pop();
      link.download = `${fileName}.${extension}`;
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  };

  const handleViewDocument = (e, url) => {
    e.preventDefault();
    e.stopPropagation();
    if (url) window.open(url, '_blank', 'noopener,noreferrer');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-3 sm:p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse space-y-4">
            <div className="h-6 sm:h-8 bg-gray-200 rounded w-1/2 sm:w-1/3 mx-auto"></div>
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 lg:gap-6">
              <div className="h-48 sm:h-64 lg:h-96 bg-gray-200 rounded-lg"></div>
              <div className="lg:col-span-3 h-48 sm:h-64 lg:h-96 bg-gray-200 rounded-lg"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 p-3 sm:p-6">
        <div className="max-w-7xl mx-auto text-center">
          <p className="text-red-500 text-sm sm:text-base">{error}</p>
        </div>
      </div>
    );
  }

  const activeDoc = categories.find(cat => cat._id === activeCategory);
  const { title: activeTitle, contactInfo: activeContactInfo } = getCurrentLanguageData(activeDoc);

  const CategoryButton = ({ category }) => {
    const { title } = getCurrentLanguageData(category);
    const isActive = category._id === activeCategory;

    if (category.enableLink && category.categoryUrl) {
      return (
        <a
          href={category.categoryUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="w-full flex items-center px-3 sm:px-4 py-2 sm:py-3 rounded-md text-left transition-colors group"
        >
          <div className="flex-1 min-w-0">
            <span className={`text-xs sm:text-sm font-medium block truncate ${
              isActive ? 'text-red-700' : 'text-gray-700 group-hover:text-gray-900'
            }`}>
              {title}
            </span>
          </div>
          <div className="ml-2 sm:ml-3 flex-shrink-0">
            <ExternalLink className="w-3 h-3 sm:w-4 sm:h-4 min-w-[12px] sm:min-w-[16px] text-gray-400 group-hover:text-red-600" />
          </div>
        </a>
      );
    }

    return (
      <button
        onClick={() => setActiveCategory(category._id)}
        className="w-full flex items-center px-3 sm:px-4 py-2 sm:py-3 rounded-md text-left transition-colors group"
      >
        <div className="flex-1 min-w-0">
          <span className={`text-xs sm:text-sm font-medium block truncate ${
            isActive ? 'text-red-700' : 'text-gray-700 group-hover:text-gray-900'
          }`}>
            {title}
          </span>
        </div>
        <div className="ml-2 sm:ml-3 flex-shrink-0">
          <ChevronRight 
            className={`w-3 h-3 sm:w-4 sm:h-4 min-w-[12px] sm:min-w-[16px] text-gray-400 transform transition-transform ${
              isActive ? 'rotate-90 text-red-600' : 'group-hover:text-gray-600'
            }`}
          />
        </div>
      </button>
    );
  };

  const DocumentItem = ({ doc }) => {
    const { title, fileUrl, fileName, extension } = getCurrentLanguageData(doc);
    const isPDF = extension?.toLowerCase() === 'pdf';

    if (doc.enableLink && doc.documentUrl) {
      return (
        <a
          href={doc.documentUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="flex items-center p-3 sm:p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-all duration-200 group"
        >
          <div className="flex-shrink-0 mr-2 sm:mr-3">
            {getFileIcon(extension)}
          </div>
          <div className="flex-1 min-w-0">
            <span className="font-medium text-gray-800 block truncate text-sm sm:text-base">
              {title}
            </span>
          </div>
          <div className="ml-2 sm:ml-3 flex-shrink-0">
            <ExternalLink className="w-4 h-4 sm:w-5 sm:h-5 min-w-[16px] sm:min-w-[20px] text-gray-400 group-hover:text-red-600" />
          </div>
        </a>
      );
    }

    return (
      <div className="flex flex-col sm:flex-row sm:items-start p-3 sm:p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-all duration-200 space-y-3 sm:space-y-0">
        <div className="flex items-start flex-1">
          <div className="flex-shrink-0 mr-2 sm:mr-3 pt-1">
            {getFileIcon(extension)}
          </div>
          <div className="flex-1">
            <span className="font-medium text-gray-800 block break-words text-sm sm:text-base">
              {title}
            </span>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row sm:ml-3 flex-shrink-0 items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-2">
          {isPDF && fileUrl && (
            <button
              onClick={(e) => handleViewDocument(e, fileUrl)}
              className="inline-flex items-center justify-center px-3 py-2 text-xs sm:text-sm font-medium text-gray-700 bg-white rounded-md border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 whitespace-nowrap"
            >
              <Eye className="w-4 h-4 sm:w-5 sm:h-5 min-w-[16px] sm:min-w-[20px] mr-1 sm:mr-2" />
              {t('viewDocument')}
            </button>
          )}
          {fileUrl && (
            <button
              onClick={(e) => handleDownload(e, fileUrl, fileName)}
              className="inline-flex items-center justify-center px-3 py-2 text-xs sm:text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 whitespace-nowrap"
            >
              <Download className="w-4 h-4 sm:w-5 sm:h-5 min-w-[16px] sm:min-w-[20px] mr-1 sm:mr-2" />
              {t('downloadDocument')}
            </button>
          )}
        </div>
      </div>
    );
  };

  return (
    <>
      <SEO
        customTitle={seoData.title}
        customDescription={seoData.description}
        language={language}
        ogType="website"
      />
      <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-3 sm:px-4 py-4 sm:py-8">
        <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-4 sm:mb-8 text-center">
          {t('title')}
        </h1>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 lg:gap-6">
          {/* Sidebar Navigation */}
          <div className="bg-white rounded-lg shadow-sm p-3 sm:p-4 order-2 lg:order-1">
            <h2 className="text-base sm:text-lg font-semibold text-gray-700 mb-3 sm:mb-4">{t('allCategories')}</h2>
            <nav className="space-y-1">
              {categories.map(category => (
                <CategoryButton key={category._id} category={category} />
              ))}
            </nav>
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-3 bg-white rounded-lg shadow-sm p-4 sm:p-6 order-1 lg:order-2">
            <div className="mb-4 sm:mb-6">
              <h2 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 mb-2 sm:mb-4">{activeTitle}</h2>
              {activeContactInfo && (
                <div className="prose prose-sm sm:prose max-w-none mb-4 sm:mb-6">
                  <PortableText value={activeContactInfo} />
                </div>
              )}
            </div>

            {activeDoc?.documents?.length > 0 ? (
              <div className="space-y-2 sm:space-y-3">
                {activeDoc.documents.map((doc, idx) => (
                  doc && <DocumentItem key={idx} doc={doc} />
                ))}
              </div>
            ) : (
              <div className="text-center text-gray-500 py-6 sm:py-8 text-sm sm:text-base">
                {t('noDocumentsAvailable')}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
    </>
  );
};

export default DocumentCategoriesViewer;