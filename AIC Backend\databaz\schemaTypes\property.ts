// schemas/property.ts
import { defineType, defineField, Rule, ValidationContext } from 'sanity';

// Types for the helper function parameters
interface FieldOptions {
  validation?: (rule: Rule) => Rule;
  options?: Record<string, any>;
  [key: string]: any;
}

// Helper function to create localized fields with proper type annotations
const createLocalizedField = (
  name: string, 
  title: string, 
  type: string, 
  options: FieldOptions = {}
) => {
  return defineField({
    name,
    title,
    type: 'object',
    fields: [
      {
        name: 'en',
        title: 'English',
        type,
        ...options
      },
      {
        name: 'sq',
        title: 'Albanian',
        type,
        ...options
      }
    ]
  });
};

export default defineType({
  name: 'property',
  title: 'Properties',
  type: 'document',
  fields: [
    // Localized text fields
    createLocalizedField('title', 'Title', 'string', {
      validation: (rule: Rule) => rule.required()
    }),
    createLocalizedField('location', 'Location Name', 'string'),
    createLocalizedField('function', 'Function', 'string'),
    
    // Modified: Status field now without predefined options list
    createLocalizedField('status', 'Development Status', 'string'),
    
    createLocalizedField('value', 'Investment Value', 'string'),
    createLocalizedField('partnerships', 'Strategic Partnerships', 'string'),
    createLocalizedField('developer', 'Developer', 'string'),
    createLocalizedField('manager', 'Manager', 'string'),
    createLocalizedField('beneficiary', 'Beneficiary', 'string'),
    createLocalizedField('designer', 'Designer', 'string'),
    
    // Non-localized fields
    defineField({
      name: 'geopoint',
      title: 'Map Location',
      description: 'Select the location on the map',
      type: 'geopoint',
    }),
    defineField({
      name: 'distanceCityCenter',
      title: 'Distance from City Center',
      type: 'object',
      fields: [
        { name: 'en', title: 'English', type: 'string' },
        { name: 'sq', title: 'Albanian', type: 'string' }
      ]
    }),
    defineField({
      name: 'distanceTIA',
      title: 'Distance from TIA',
      type: 'object',
      fields: [
        { name: 'en', title: 'English', type: 'string' },
        { name: 'sq', title: 'Albanian', type: 'string' }
      ]
    }),
    
    // Modified: Area measurements with localization
    createLocalizedField('areaSize', 'Area Size (m²)', 'string', {
      description: 'Enter TBD if not determined yet'
    }),
    createLocalizedField('constructionArea', 'Construction Area (m²)', 'string', {
      description: 'Enter TBD if not determined yet'
    }),
    createLocalizedField('businessSpace', 'Business Spaces (m²)', 'string', {
      description: 'Enter TBD if not determined yet'
    }),
    createLocalizedField('hotelSpace', 'Hotel Space (m²)', 'string', {
      description: 'Enter TBD if not determined yet'
    }),
    createLocalizedField('officeSpace', 'Office Space (m²)', 'string', {
      description: 'Enter TBD if not determined yet'
    }),
    createLocalizedField('residentialSpace', 'Residential Space (m²)', 'string', {
      description: 'Enter TBD if not determined yet'
    }),
    
    // Images
    defineField({
      name: 'projectImage',
      title: 'Project Image',
      type: 'object',
      fields: [
        {
          name: 'en',
          title: 'English',
          type: 'image',
          options: { hotspot: true }
        },
        {
          name: 'sq',
          title: 'Albanian',
          type: 'image',
          options: { hotspot: true }
        }
      ],
      description: 'The main project image/rendering'
    }),
    defineField({
      name: 'mapImage',
      title: 'Map Image',
      type: 'object',
      fields: [
        {
          name: 'en',
          title: 'English',
          type: 'image',
          options: { hotspot: true }
        },
        {
          name: 'sq',
          title: 'Albanian',
          type: 'image',
          options: { hotspot: true }
        }
      ],
      description: 'Map or location image for the property'
    }),
    
    // Document fields
    defineField({
      name: 'developmentPermit',
      title: 'Development Permit',
      type: 'object',
      fields: [
        {
          name: 'en',
          title: 'English',
          type: 'image',
          options: { hotspot: true }
        },
        {
          name: 'sq',
          title: 'Albanian',
          type: 'image',
          options: { hotspot: true }
        }
      ],
      description: 'Development permit document image'
    }),
    defineField({
      name: 'constructionPermit',
      title: 'Construction Permit',
      type: 'object',
      fields: [
        {
          name: 'en',
          title: 'English',
          type: 'image',
          options: { hotspot: true }
        },
        {
          name: 'sq',
          title: 'Albanian',
          type: 'image',
          options: { hotspot: true }
        }
      ],
      description: 'Construction permit document image'
    }),
    
    // New PDF field
    defineField({
      name: 'projectPDF',
      title: 'Project PDF',
      type: 'object',
      fields: [
        {
          name: 'en',
          title: 'English',
          type: 'file',
          options: { accept: '.pdf' }
        },
        {
          name: 'sq',
          title: 'Albanian',
          type: 'file',
          options: { accept: '.pdf' }
        }
      ],
      description: 'Upload project PDF documents'
    }),
    
    // Gallery - can be the same for both languages
    defineField({
      name: 'gallery',
      title: 'Additional Images',
      description: 'Add multiple images to create a gallery',
      type: 'array',
      of: [{ 
        type: 'image', 
        options: { hotspot: true }
      }]
    }),
    
    // Localized rich text descriptions
    defineField({
      name: 'description',
      title: 'Description',
      description: 'Detailed description of the property',
      type: 'object',
      fields: [
        {
          name: 'en',
          title: 'English',
          type: 'array',
          of: [{ type: 'block' }]
        },
        {
          name: 'sq',
          title: 'Albanian',
          type: 'array',
          of: [{ type: 'block' }]
        }
      ]
    })
  ],
  preview: {
    select: {
      title: 'title.en',
      subtitle: 'location.en',
      media: 'projectImage.en'
    },
    prepare({ title, subtitle, media }) {
      return {
        title: title || 'No title',
        subtitle: subtitle || 'No location',
        media
      };
    }
  }
});