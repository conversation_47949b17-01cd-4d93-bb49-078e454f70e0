.newspage {
  font-family: Arial, sans-serif;
  color: #333;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.newspage-category {
  margin-bottom: 40px;
  position: relative;
}

.newspage-category h2 {
  background-color: #e41e26;
  color: white;
  padding: 10px 20px;
  margin: 0;
  font-size: 1.2em;
  font-weight: normal;
}

.newspage-grid-container {
  overflow: hidden;
  background-color: #f0f0f0;
  padding: 20px;
}

.newspage-grid {
  display: flex;
  gap: 22px;
  width: 100%;
}

.newspage-item {
  flex: 0 0 calc(33.333% - 14px);
  background: white;
  border: 1px solid #ddd;
  padding: 20px;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease;
  height: 340px;
  overflow: hidden;
}

.newspage-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.newspage-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
  margin-bottom: 10px;
}

.newspage-content {
  display: flex;
  flex-direction: column;
  height: calc(100% - 160px);
}

.newspage-item-title {
  font-size: 1em;
  margin: 0 0 10px 0;
  color: #e41e26;
}

.newspage-excerpt {
  font-size: 0.9em;
  color: #666;
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.newspage-read-more {
  text-align: right;
  color: #e41e26;
  text-decoration: none;
  font-weight: bold;
  font-size: 0.9em;
  margin-top: auto;
  transition: color 0.3s ease;
}

.newspage-read-more:hover {
  color: #b51820;
}

.newspage-empty-message {
  color: #999;
  font-style: italic;
  padding: 20px;
}

.newspage-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background: #e41e26;
  color: white;
  border: none;
  font-size: 1.5em;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
  z-index: 10;
}

.newspage-nav:hover:not(:disabled) {
  background-color: #b51820;
}

.newspage-nav:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.newspage-prev {
  left: -20px;
}

.newspage-next {
  right: -20px;
}

@media (max-width: 768px) {
  .newspage-category-mobile {
    margin-bottom: 40px;
  }

  .newspage-mobile-container {
    position: relative;
    background-color: #f0f0f0;
    padding: 20px;
  }

  .newspage-mobile-item {
    background: white;
    border: 1px solid #ddd;
    padding: 20px;
  }

  .newspage-mobile-nav {
    position: relative;
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
  }

  .newspage-mobile-nav .newspage-nav {
    position: static;
    transform: none;
  }

  .newspage-mobile-nav .newspage-prev {
    margin-right: 10px;
  }

  .newspage-mobile-nav .newspage-next {
    margin-left: 10px;
  }
}