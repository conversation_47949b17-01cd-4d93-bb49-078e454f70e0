{"name": "<PERSON><PERSON><PERSON>", "version": "6.0.1", "type": "module", "tshy": {"main": true, "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "bin": "./dist/esm/bin.mjs", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "files": ["dist"], "description": "A deep deletion module for node (like `rm -rf`)", "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC", "repository": "git://github.com/isaacs/rimraf.git", "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "format": "prettier --write . --log-level warn", "benchmark": "node benchmark/index.js", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "prettier": {"experimentalTernaries": true, "semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/node": "^20.14.10", "mkdirp": "^3.0.1", "prettier": "^3.3.2", "tap": "^20.0.3", "tshy": "^2.0.1", "typedoc": "^0.26.3"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "engines": {"node": "20 || >=22"}, "dependencies": {"glob": "^11.0.0", "package-json-from-dist": "^1.0.0"}, "keywords": ["rm", "rm -rf", "rm -fr", "remove", "directory", "cli", "rmdir", "recursive"], "module": "./dist/esm/index.js"}