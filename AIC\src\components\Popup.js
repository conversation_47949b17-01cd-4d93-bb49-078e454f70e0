import React, { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Presentation } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useLanguage } from "./languageUtils";
import sanityClient from '../sanity'; // Changed from import { client } from "../sanity"

const WelcomePopup = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeProject, setActiveProject] = useState(0);
  const [showButton, setShowButton] = useState(false);
  const [showText, setShowText] = useState(true);
  const [popupData, setPopupData] = useState(null);
  const [loading, setLoading] = useState(true);
  const popupRef = useRef(null);
  const navigate = useNavigate();
  const { language } = useLanguage();

  // Fetch popup data from Sanity
  useEffect(() => {
    const fetchPopupData = async () => {
      try {
        // Query to get the active welcome popup
        const query = `*[_type == "welcomePopup" && enabled == true][0]{
          displayFrequency,
          displayDelay,
          projects[]{
            name,
            subtitle,
            date,
            "pathEn": path.en,
            "pathSq": path.sq,
            mediaType,
            "imageEn": image.en.asset->url,
            "imageSq": image.sq.asset->url,
            "videoEn": video.en.asset->url,
            "videoSq": video.sq.asset->url,
            "videoUrlEn": videoUrl.en,
            "videoUrlSq": videoUrl.sq,
            "videoThumbnailEn": videoThumbnail.en.asset->url,
            "videoThumbnailSq": videoThumbnail.sq.asset->url
          },
          uiText
        }`;
        
        // Changed from client.fetch to sanityClient.fetch
        const result = await sanityClient.fetch(query);
        setPopupData(result);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching welcome popup:", error);
        setLoading(false);
      }
    };

    fetchPopupData();
  }, []);

  // Handle outside clicks
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  // Check if we should show the popup
  useEffect(() => {
    if (loading || !popupData) return;

    const checkUserStatus = () => {
      const lastVisit = localStorage.getItem("lastVisit");
      const currentTime = new Date().getTime();
      // Convert days to milliseconds (use Sanity value or default to 7 days)
      const showAgainAfter = (popupData.displayFrequency || 7) * 24 * 60 * 60 * 1000;

      if (!lastVisit || currentTime - parseInt(lastVisit) > showAgainAfter) {
        // Use the delay from Sanity or default to 800ms
        setTimeout(() => {
          setIsOpen(true);
          localStorage.setItem("lastVisit", currentTime.toString());
        }, popupData.displayDelay || 800);
      }
      setShowButton(!!lastVisit);
    };

    checkUserStatus();
  }, [loading, popupData]);

  // If still loading or no data, don't render anything
  if (loading || !popupData) return null;

  // Get the current project based on active index
  const currentProject = popupData.projects[activeProject];
  
  // Get the media URL based on language
  const getMediaUrl = (project) => {
    if (project.mediaType === 'video') {
      // First check for uploaded video
      if (language === 'sq' && project.videoSq) {
        return project.videoSq;
      } else if (language === 'en' && project.videoEn) {
        return project.videoEn;
      }
      
      // Then check for video URL
      if (language === 'sq' && project.videoUrlSq) {
        return project.videoUrlSq;
      } else if (language === 'en' && project.videoUrlEn) {
        return project.videoUrlEn;
      }
      
      // Fallback to thumbnail
      if (language === 'sq' && project.videoThumbnailSq) {
        return project.videoThumbnailSq;
      } else if (language === 'en' && project.videoThumbnailEn) {
        return project.videoThumbnailEn;
      }
    } else {
      // Image type
      if (language === 'sq' && project.imageSq) {
        return project.imageSq;
      } else if (language === 'en' && project.imageEn) {
        return project.imageEn;
      }
    }
    
    // Fallback if no match found
    return language === 'sq' ? project.imageSq || project.imageEn : project.imageEn || project.imageSq;
  };

  // Get text content based on language
  const getText = (field) => {
    if (!popupData.uiText || !popupData.uiText[field]) return '';
    return language === 'sq' ? 
      popupData.uiText[field].sq : 
      popupData.uiText[field].en;
  };

  const getProjectText = (project, field) => {
    if (!project || !project[field]) return '';
    return language === 'sq' ? 
      (project[field].sq || '') : 
      (project[field].en || '');
  };

  const switchProject = () => {
    setActiveProject((prev) =>
      prev === popupData.projects.length - 1 ? 0 : prev + 1
    );
  };

  const handleClose = () => {
    setIsOpen(false);
    setShowButton(true);
  };

  const handleLearnMore = () => {
    // Use the language-specific path
    const projectPath = language === 'sq' ? currentProject.pathSq : currentProject.pathEn;
    navigate(`/${language}/${projectPath}`);
    handleClose();
  };

  // Check if we have valid data
  if (!currentProject) return null;

  return (
    <>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="fixed inset-0 bg-black/70 flex items-center justify-center z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              ref={popupRef}
              className="bg-white w-[90%] md:w-[500px] rounded-2xl overflow-hidden shadow-2xl max-h-[85vh] md:h-[85vh]"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex flex-col h-full">
                <div
                  className="relative bg-black w-full flex-grow"
                  style={{
                    minHeight: "60vh",
                    maxHeight: "65vh",
                  }}
                >
                  {currentProject.mediaType === 'video' ? (
                    <video
                      src={getMediaUrl(currentProject)}
                      poster={language === 'sq' ? currentProject.videoThumbnailSq : currentProject.videoThumbnailEn}
                      controls
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <img
                      src={getMediaUrl(currentProject)}
                      alt={getProjectText(currentProject, 'name')}
                      className="w-full h-full object-cover"
                    />
                  )}
                </div>

                <div className="p-3 md:p-4">
                  <h2 className="text-lg md:text-xl font-bold text-gray-900">
                    {getProjectText(currentProject, 'name')}
                  </h2>
                  <p className="text-xs md:text-sm text-gray-600 mt-1">
                    {getProjectText(currentProject, 'subtitle')}
                  </p>
                  <p className="text-xs md:text-sm text-red-600 font-medium mt-1">
                    {currentProject.date ? new Date(currentProject.date).toLocaleDateString(
                      language === 'sq' ? 'sq-AL' : 'en-US',
                      { day: 'numeric', month: 'long', year: 'numeric' }
                    ).toUpperCase() : ''}
                  </p>
                </div>

                <div className="p-3 md:p-4 pt-0">
                  <div className="flex gap-2">
                    <motion.button
                      onClick={switchProject}
                      className="flex-1 px-2 md:px-3 py-2 md:py-2.5 text-xs md:text-sm border-2 border-red-600 text-red-600 rounded-xl font-medium"
                      whileTap={{ scale: 0.98 }}
                    >
                      {getText('switchText')}
                    </motion.button>

                    <motion.button
                      onClick={handleLearnMore}
                      className="flex-1 px-2 md:px-3 py-2 md:py-2.5 text-xs md:text-sm bg-red-600 text-white rounded-xl font-medium"
                      whileTap={{ scale: 0.98 }}
                    >
                      {getText('buttonText')}
                    </motion.button>
                  </div>
                </div>
              </div>

              <button
                onClick={handleClose}
                className="absolute top-2 right-2 md:top-4 md:right-4 w-7 h-7 md:w-8 md:h-8 flex items-center justify-center rounded-full bg-black/20"
              >
                <span className="text-white text-lg md:text-xl">×</span>
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {showButton && !isOpen && (
          <div className="fixed bottom-4 md:bottom-6 right-4 md:right-6 flex items-center gap-3 z-40">
            <AnimatePresence>
              {showText && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  className="bg-white/90 text-gray-700 text-xs md:text-sm py-2 px-3 md:px-4 rounded-full shadow-md hidden md:block"
                  onAnimationComplete={() => {
                    setTimeout(() => {
                      setShowText(false);
                    }, 5000);
                  }}
                >
                  {getText('hintText')}
                </motion.div>
              )}
            </AnimatePresence>

            <motion.button
              className="w-10 h-10 md:w-12 md:h-12 bg-red-600 text-white rounded-full shadow-lg flex items-center justify-center hover:bg-red-700 transition-all"
              onClick={() => setIsOpen(true)}
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0, opacity: 0 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Presentation size={18} className="md:w-5 md:h-5" />
            </motion.button>
          </div>
        )}
      </AnimatePresence>
    </>
  );
};

export default WelcomePopup;