import React, { useState, useContext, createContext, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useLanguage } from './languageUtils';
import sanityClient from '../sanity';

export const GlobalLanguageContext = createContext();

export const GlobalLanguageProvider = ({ children }) => {
  const [globalLanguage, setGlobalLanguage] = useState('en');

  return (
    <GlobalLanguageContext.Provider value={{ globalLanguage, setGlobalLanguage }}>
      {children}
    </GlobalLanguageContext.Provider>
  );
};

const LanguageSwitcher = () => {
  const { language, changeLanguage, translations } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();
  const [isFetching, setIsFetching] = useState(false);
  const { globalLanguage, setGlobalLanguage } = useContext(GlobalLanguageContext);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 1024);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 1024);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Fetches translation reference from Sanity for content types
  const fetchSanityTranslation = async (slug, currentLang, contentType) => {
    try { 
      const post = await sanityClient.fetch(
        `*[slug.current == $slug && language == $currentLang && _type == $contentType][0]{
          "translationRef": translationRef->{
            "slug": slug.current, 
            language,
            "permalink": permalink.current
          }
        }`,
        { slug, currentLang, contentType }
      );
      return post?.translationRef;
    } catch (err) {
      console.error('Error fetching Sanity translation:', err);
      return null;
    }
  };

  // Identifies content types from path segments
  const getContentType = (segment) => {
    if (['news', 'lajme'].includes(segment)) return 'newsPost';
    if (['projects', 'projekte'].includes(segment)) return 'projectsPost';
    if (['calls', 'thirrje'].includes(segment)) return 'callsPost';
    return null;
  };

  // Translates a single route segment between languages
  const translateSegment = (segment, fromLang, toLang) => {
    if (!segment) return '';
    
    const fromRoutes = translations[fromLang].routes;
    const toRoutes = translations[toLang].routes;
    
    // Find the key by value in the source language
    const key = Object.keys(fromRoutes).find(key => fromRoutes[key] === segment);
    
    // Return translated segment if found, otherwise return original
    return key && toRoutes[key] ? toRoutes[key] : segment;
  };

  // Helper function to translate nested paths
  const translateNestedPath = (path, fromLang, toLang) => {
    if (!path) return '';
    
    // First check if the entire path has a direct translation
    const segments = path.split('/');
    const lastSegment = segments[segments.length - 1];
    
    // Try to translate the last segment
    const translatedSegment = translateSegment(lastSegment, fromLang, toLang);
    
    // Replace the last segment with its translation
    segments[segments.length - 1] = translatedSegment;
    
    return segments.join('/');
  };

  // Main language switching function
  const switchLanguage = async (newLang) => {
    if (isFetching || globalLanguage === newLang) return;
    
    setIsFetching(true);
    console.log('Switching language to:', newLang);
    console.log('Current path:', location.pathname);
    
    // Extract path parts properly
    const pathParts = location.pathname.split('/').filter(Boolean);
    const currentLang = pathParts[0];
    const pathSegments = pathParts.slice(1); // Get all segments after language code
    
    console.log('Current language:', currentLang);
    console.log('Path segments:', pathSegments);
    
    let newPath = '';
    
    // Case 1: Empty path (homepage)
    if (pathSegments.length === 0) {
      newPath = `/${newLang}`;
    }
    // Case 2: Content with possible translation reference (news, projects)
    else if (pathSegments.length > 1) {
      const mainCategory = pathSegments[0];
      const contentType = getContentType(mainCategory);
      
      // Translate the main category
      const translatedMainCategory = translateSegment(mainCategory, language, newLang);
      
      // Define known route segments that should be translated directly
      const knownRouteSegments = [
        'strategic-projects', 'projekte-strategjike', 
        'revitalization', 'rindërtim-zhvillim-urban', 
        'real-estate', 'projekte-te-pasurive',
        'open-calls', 'thirrje-te-hapura',
        'upcoming-calls', 'thirrje-te-ardhshme', 
        'ongoing', 'ne-vazhdim',
        'closed', 'te-mbyllura'
      ];
      
      // Case 2.1: Content with slug that might have translation reference
      if (contentType && pathSegments.length === 2 && !knownRouteSegments.includes(pathSegments[1])) {
        const slug = pathSegments[1];
        const translationRef = await fetchSanityTranslation(slug, language, contentType);
        
        if (translationRef && translationRef.language === newLang) {
          // Use permalink if available, otherwise use translated slug
          newPath = `/${newLang}/${translatedMainCategory}/${translationRef.permalink || translationRef.slug}`;
        } else {
          // No translation found, keep original slug
          newPath = `/${newLang}/${translatedMainCategory}/${slug}`;
        }
      }
      // Case 2.2: Nested routes (projects/strategic-projects, etc.)
      else {
        let translatedPath = [];
        translatedPath.push(translatedMainCategory);
        
        // Handle each level of nesting
        for (let i = 1; i < pathSegments.length; i++) {
          // Build the current path up to this segment
          const currentPath = pathSegments.slice(0, i).join('/');
          const segment = pathSegments[i];
          
          // Try to translate with context of parent segments
          let translated;
          
          // Check if this is a known nested route
          if (i === 1 && knownRouteSegments.includes(segment)) {
            translated = translateSegment(segment, language, newLang);
          }
          // For third level (real-estate/coastal-areas, etc.)
          else if (i === 2 && ['coastal-areas', 'zonat-bregdetare', 'downtown-areas', 'zonat-qendrore',
                              'public-service-complex', 'kompleksi-sherbimit-publik'].includes(segment)) {
            translated = translateSegment(segment, language, newLang);
          }
          // Default translation
          else {
            translated = translateSegment(segment, language, newLang);
          }
          
          translatedPath.push(translated);
        }
        
        newPath = `/${newLang}/${translatedPath.join('/')}`;
      }
    }
    // Case 3: Simple one-level path
    else {
      const translatedSegment = translateSegment(pathSegments[0], language, newLang);
      newPath = `/${newLang}/${translatedSegment}`;
    }
    
    console.log('New path:', newPath);
    
    changeLanguage(newLang);
    setGlobalLanguage(newLang);
    navigate(newPath, { replace: true });
    setIsFetching(false);
  };

  // UI rendering for language switcher buttons
  const buttonClass = (lang) => {
    const baseClass = isFetching ? 'opacity-50 cursor-not-allowed' : '';
    const mobileClass = `px-3 py-1 bg-red-600 text-white rounded-md ${baseClass} ${
      globalLanguage === lang ? 'font-bold underline' : 'font-normal'
    }`;
    const desktopClass = `px-1 py-0.5 ${
      globalLanguage === lang ? 'text-red-600 font-bold' : 'text-white'
    } ${baseClass}`;

    return isMobile ? mobileClass : desktopClass;
  };

  return (
    <div className="flex items-center space-x-2">
      <button 
        onClick={() => switchLanguage('en')} 
        className={buttonClass('en')}
        disabled={isFetching}
      >
        EN
      </button>
      <button 
        onClick={() => switchLanguage('sq')} 
        className={buttonClass('sq')}
        disabled={isFetching}
      >
        SQ
      </button>
    </div>
  );
};

export default LanguageSwitcher;