import React, { useState, useEffect, useContext } from 'react';
import sanityClient from '../../sanity';
import { GlobalLanguageContext } from '../../components/LanguageSwitcher';
import { motion, useAnimation } from 'framer-motion';
import { useInView } from 'react-intersection-observer';

const CareersPage = () => {
  const { globalLanguage } = useContext(GlobalLanguageContext);
  const [careerPosts, setCareerPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const controls = useAnimation();
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const content = {
    en: {
      title: "Join our growing team",
      subtitle: "Explore career opportunities",
      readMore: "View Position",
      viewAllPositions: "View all open positions",
      positions: "Open positions",
      noCareersAvailable: "Currently, there are no career opportunities available.",
      loading: "Loading opportunities...",
      errorMessage: "Failed to load positions.",
      defaultImageAlt: "Career opportunity",
      department: "Department",
      fullTime: "Full time"
    },
    sq: {
      title: "BËHU PJESË E EKIPIT TONË",
      subtitle: "Eksploro mundësitë e karrierës",
      readMore: "Shiko Pozicionin",
      viewAllPositions: "Shiko të gjitha pozicionet e lira",
      positions: "Pozicione të lira",
      noCareersAvailable: "Aktualisht, nuk ka mundësi karriere të disponueshme." ,
      loading: "Duke ngarkuar mundësitë...",
      errorMessage: "Dështoi në ngarkimin e pozicioneve.",
      defaultImageAlt: "Mundësi karriere",
      department: "Departamenti",
      fullTime: "Kohë e plotë"
    }
  };

  useEffect(() => {
    if (inView) {
      controls.start('visible');
    }
  }, [controls, inView]);

  useEffect(() => {
    setLoading(true);
    sanityClient.fetch(
      `*[_type == "careerPost" && language == $language] | order(publishDate desc) {
        title,
        slug,
        mainImage {
          asset->{
            _id,
            url
          },
          alt
        },
        body,
        publishDate,
        department,
        employmentType,
        "excerpt": pt::text(body)[0...150]
      }`,
      { language: globalLanguage }
    )
    .then((data) => {
      setCareerPosts(data);
      setLoading(false);
    })
    .catch((err) => {
      console.error(err);
      setError(content[globalLanguage].errorMessage);
      setLoading(false);
    });
  }, [globalLanguage]);

  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  };

  const renderCareerItem = (post, index) => (
    <motion.div 
      key={post.slug.current} 
      className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition duration-300 ease-in-out transform hover:-translate-y-2"
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
    >
      <div className="relative h-48">
        <img
          src={post.mainImage?.asset?.url || 'https://via.placeholder.com/400x200?text=Career+Opportunity'}
          alt={post.mainImage?.alt || content[globalLanguage].defaultImageAlt}
          className="w-full h-full object-cover"
        />
        <div className="absolute top-3 right-3">
          <span className="bg-red-600 text-white px-2 py-1 rounded-full text-xs font-semibold">
            {post.employmentType || content[globalLanguage].fullTime}
          </span>
        </div>
      </div>
      
      <div className="p-6">
        {post.publishDate && (
          <p className="text-sm text-gray-500 mb-2">
            {formatDate(post.publishDate)}
          </p>
        )}
        
        {post.department && (
          <div className="mb-3">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              {content[globalLanguage].department}: {post.department}
            </span>
          </div>
        )}
        
        <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
          {post.title}
        </h3>
        
        <p className="text-gray-600 text-sm mb-4 line-clamp-3">
          {post.excerpt}
        </p>
        
        <div className="flex justify-between items-center">
          <a 
            href={`/${globalLanguage === 'en' ? 'en/news' : 'sq/lajme'}/${post.slug.current}`} 
            className="inline-flex items-center px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 transition duration-200"
          >
            {content[globalLanguage].readMore}
            <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </a>
        </div>
      </div>
    </motion.div>
  );

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-20">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
          <p className="mt-4 text-gray-600">{content[globalLanguage].loading}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-20">
          <p className="text-red-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 font-sans text-gray-800">
      {/* Hero Section */}
      <div className="text-center mb-16">
        <motion.h1 
          className="text-4xl md:text-5xl font-bold text-gray-900 mb-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {content[globalLanguage].title}
        </motion.h1>
        <motion.p 
          className="text-xl text-gray-600 max-w-2xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {content[globalLanguage].subtitle}
        </motion.p>
      </div>

      {/* Career Positions Section */}
      <motion.div 
        ref={ref}
        className="mb-16"
        initial="hidden"
        animate={controls}
        variants={{
          hidden: { opacity: 0 },
          visible: { 
            opacity: 1,
            transition: { 
              when: "beforeChildren",
              staggerChildren: 0.1
            }
          }
        }}
      >
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900">
            {content[globalLanguage].positions}
          </h2>
          {careerPosts.length > 4 && (
            <a 
              href={`/${globalLanguage === 'en' ? 'en/careers' : 'sq/karriera'}/all`}
              className="text-red-600 hover:text-red-700 font-medium flex items-center"
            >
              {content[globalLanguage].viewAllPositions}
              <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </a>
          )}
        </div>

        {careerPosts.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {careerPosts.slice(0, 4).map((post, index) => renderCareerItem(post, index))}
          </div>
        ) : (
          <motion.div 
            className="text-center py-16 bg-gray-50 rounded-lg"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6" />
            </svg>
            <p className="text-gray-600 text-lg">
              {content[globalLanguage].noCareersAvailable}
            </p>
          </motion.div>
        )}
      </motion.div>
    </div>
  );
};

export default CareersPage;