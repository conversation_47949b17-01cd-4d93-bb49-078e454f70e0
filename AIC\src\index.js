// All imports must be at the very top
import React from 'react';
import ReactD<PERSON> from 'react-dom/client';
import { I18nextProvider } from 'react-i18next';
import i18n from './i18n';
import './index.css';
import App from './App';
import reportWebVitals from './reportWebVitals';

// Enhanced React DevTools hiding - handles existing DevTools
if (typeof window !== 'undefined') {
  // Method 1: Try to disable existing hook
  try {
    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      // If it exists, disable its functionality
      window.__REACT_DEVTOOLS_GLOBAL_HOOK__.isDisabled = true;
      window.__REACT_DEVTOOLS_GLOBAL_HOOK__.supportsFiber = true;
      
      // Override key methods
      if (typeof window.__REACT_DEVTOOLS_GLOBAL_HOOK__.inject === 'function') {
        window.__REACT_DEVTOOLS_GLOBAL_HOOK__.inject = () => {};
      }
      if (typeof window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberRoot === 'function') {
        window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberRoot = () => {};
      }
      if (typeof window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberUnmount === 'function') {
        window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberUnmount = () => {};
      }
    }
  } catch (error) {
    // If we can't modify the existing hook, continue silently
  }

  // Method 2: Try to create if it doesn't exist
  try {
    if (!window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      Object.defineProperty(window, '__REACT_DEVTOOLS_GLOBAL_HOOK__', {
        value: {
          isDisabled: true,
          supportsFiber: true,
          inject: () => {},
          onCommitFiberRoot: () => {},
          onCommitFiberUnmount: () => {},
        },
        writable: false,
        configurable: false
      });
    }
  } catch (error) {
    // If we can't create it, continue silently
  }

  // Method 3: Additional hiding techniques
  setTimeout(() => {
    try {
      // Remove React from global scope
      delete window.React;
      delete window.ReactDOM;
      
      // Hide React-related properties
      Object.defineProperty(window, 'React', {
        get: () => undefined,
        configurable: false
      });
      
      Object.defineProperty(window, 'ReactDOM', {
        get: () => undefined,
        configurable: false
      });
    } catch (error) {
      // Continue silently if we can't hide these
    }
  }, 100);
}

// Set page title and favicon
document.title = 'Albanian Investment Corporation';

const link = document.createElement('link');
link.rel = 'icon';
link.type = 'image/png';
link.href = `/Asset4.png`;
document.head.appendChild(link);

// Render the app
const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <I18nextProvider i18n={i18n}>
      <App />
    </I18nextProvider>
  </React.StrictMode>
);

// Performance monitoring
reportWebVitals();