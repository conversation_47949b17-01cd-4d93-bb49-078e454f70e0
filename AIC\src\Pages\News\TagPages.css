.tag-page {
    margin: 20px;
  }
  
  .tag-title {
    font-size: 24px;
    margin-bottom: 20px;
  }
  
  .news-grid {
    display: flex;
    flex-wrap: wrap;
  }
  
  .news-item {
    margin: 10px;
    padding: 10px;
    border: 1px solid #ccc;
    width: 300px;
    border-radius: 5px;
  }
  
  .news-image {
    width: 100%;
    height: 150px;
    object-fit: cover;
    margin-bottom: 10px;
  }
  
  .news-item-title {
    font-size: 20px;
    margin-bottom: 10px;
  }
  
  .news-excerpt {
    font-size: 14px;
    color: #666;
  }
  
  .news-read-more {
    color: #007bff;
    text-decoration: none;
  }
  