import React, { createContext, useContext, useState } from 'react';

const translations = {
  en: {
    menu: {
      aboutUs: "About us",
      news: "News",
      projects: "Projects Portfolio",
      calls: "Calls",
      team: "Team",
      networking: "Networking",
      transparency: "Transparency program",
      openData: "Open Data",
      map: "Projects Map",
      contact: "Contact"
    },
    submenus: {
      aboutUs: {
        visionMission: "Our Vision & Mission",
        whyChooseAIC: "Why Choose AIC",
        lawsRegulations: "Laws and Regulations",
        transparencyProgram: "Transparency Program",
        consultancy:"Consultancy AIC"
      },
      projects: {
        strategicProjects: "Strategic Projects",
        revitalization: "Revitalization and Reurbanization",
        realEstate: "Real Estate Projects",
        realEstateSubcategories: {
          coastal: "Albanian Coastal Areas",
          downtown: "Downtown Areas",
          publicService: "Public Service Complex"
        }
      },
      calls: {
        openCalls: "Open Calls",
        upcomingCalls: "Upcoming Calls",
        ongoing: "Ongoing",
        closedCalls: "Closed Calls"
      },
      team: {
        institutionalStructure: "Institutional Structure",
        meetThe<PERSON>taff: "Meet the Staff",
        joinUs: "Join Us"
      },
      networking: {
        partners: "Partners",
        lineMinistersRelationship: "Relationship with Line Ministers",
        aidaRelationship: "Relationship with AIDA",
        adfRelationship: "Relationship with ADF"
      }
    },
    routes: {
      "about-us": "about-us",
      "vision-and-mission": "vision-and-mission",
      "why-choose-aic": "why-choose-aic",
      "law-and-regulations": "law-and-regulations",
      "news": "news",
      "projects": "projects",
      "strategic-projects": "strategic-projects",
      "revitalization": "revitalization",
      "real-estate": "real-estate",
      "coastal-areas": "coastal-areas",
      "downtown-areas": "downtown-areas",
      "public-service-complex": "public-service-complex",
      "calls": "calls",
      "open-calls": "open-calls",
      "upcoming-calls": "upcoming-calls",
      "ongoing": "ongoing",
      "closed": "closed",
      "team": "team",
      "institutional-structure": "institutional-structure",
      "meet-the-staff": "meet-the-staff",
      "join-us": "join-us",
      "networking": "networking",
      "partners": "partners",
      "relationship-with-line-ministers": "relationship-with-line-ministers",
      "transparency": "transparency-program",
      "open-data": "open-data",
      "contact": "contact",
      "consultancy":"consultancy",
      "tags": "tags",
      "map": "map"
    },
    topInfo: {
      workHours: "Mon - Fri : 08:00 - 17:00",
      email: "<EMAIL>",
      address: "St. Ibrahim Rugova 5, Sky Tower 7/1, Tirana, Albania"
    },
    common: {
      investNow: "Invest now!",
      search: "Search..."
    }
  },
  sq: {
    menu: {
      aboutUs: "Rreth nesh",
      news: "Lajme",
      projects: "Portofoli i Projekteve",
      calls: "Thirrje",
      team: "Ekipi",
      networking: "Bashkëpunëtorët",
      transparency: "Programi i transparences",
      openData: "Të Dhënat e Hapura",
      map: "Harta e Projekteve",
      contact: "Kontakt"
      
    },
    submenus: {
      aboutUs: {
        visionMission: "Vizioni dhe Misioni Ynë",
        whyChooseAIC: "Pse të Zgjidhni KISH",
        lawsRegulations: "Ligjet dhe Rregulloret",
        consultancy: "Konsulenca KISH",
      },
      projects: {
        strategicProjects: "Projekte investimi strategjike",
        revitalization: "Projekte investimi me Institucionet Publike",
        realEstate: "Iniciativa të Zhvillimit të Territorit",
        realEstateSubcategories: {
          coastal: "Zonat Bregdetare Shqiptare",
          downtown: "Qëndra Urbane",
          publicService: "Kompleksi i Shërbimit Publik"
        }
      },
      calls: {
        openCalls: "Thirrje Të Hapura",
        upcomingCalls: "Thirrje Të Ardhshme",
        ongoing: "Thirrje Në Vazhdim",
        closedCalls: "Thirrje të Mbyllura"
      },
      team: {
        institutionalStructure: "Struktura Institucionale",
        meetTheStaff: "Njihuni me Stafin",
        joinUs: "Bashkohuni me Ne"
      },
      networking: {
        partners: "Partneret",
        lineMinistersRelationship: "Marrëdhënia me Ministritë e Linjës",
        aidaRelationship: "Marrëdhënia me AIDA",
        adfRelationship: "Marrëdhënia me ADF"
      }
    },
    routes: {
      "about-us": "rreth-nesh",
      "vision-and-mission": "vizioni-dhe-misioni",
      "why-choose-aic": "pse-te-zgjidhni-aic",
      "law-and-regulations": "ligjet-dhe-rregulloret",
      "transparency": "programi-i-transparences", 
      "open-data": "te-dhenat-e-hapura",
      "news": "lajme",
      "projects": "projekte",
      "strategic-projects": "projekte-strategjike",
      "revitalization": "rindertim-zhvillim-urban",
      "real-estate": "projekte-te-pasurive",
      "coastal-areas": "zonat-bregdetare",
      "downtown-areas": "zonat-qendrore",
      "public-service-complex": "kompleksi-sherbimit-publik",
      "calls": "thirrje",
      "open-calls": "thirrje-te-hapura",
      "upcoming-calls": "thirrje-te-ardhshme",
      "ongoing": "ne-vazhdim",
      "closed": "te-mbyllura",
      "team": "ekipi",
      "institutional-structure": "struktura-institucionale",
      "meet-the-staff": "njihuni-me-stafin",
      "join-us": "bashkohuni-me-ne",
      "networking": "bashkepunetoret",
      "partners": "partneret",
      "relationship-with-line-ministers": "marredhenia-me-ministrite-e-linjes",
      "contact": "kontakt",
      "tags": "etiketat",
      "consultancy":"konsulenca",
      "map": "harta"
    },
    topInfo: {
      workHours: "E Hënë - E Premte : 08:00 - 17:00",
      email: "<EMAIL>",
      address: "Rr. Ibrahim Rugova 5, Sky Tower 7/1, Tiranë, Shqipëri"
    },
    common: {
      investNow: "Investo tani!",
      search: "Kërko..."
    }
  }
};

const LanguageContext = createContext();

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState('en');

  const changeLanguage = (lang) => {
    setLanguage(lang);
  };

  return (
    <LanguageContext.Provider value={{ language, changeLanguage, translations }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => useContext(LanguageContext);

export const translateRoute = (route, fromLang, toLang) => {
  if (!route || typeof route !== 'string') {
    console.warn('Invalid route provided to translateRoute:', route);
    return '';
  }

  // Special case handling for networking route
  if (route === 'networking' && toLang === 'sq') {
    return 'bashkepunetoret'; // Return the simplified version without special chars
  }
  
  // Handle the specific case when switching back to English
  if (route === 'bashkepunetoret' && toLang === 'en') {
    return 'networking';
  }

  // Special case handling for open-data route
  if (route === 'open-data' && toLang === 'sq') {
    return 'te-dhenat-e-hapura';
  }
  
  if (route === 'te-dhenat-e-hapura' && toLang === 'en') {
    return 'open-data';
  }

  const segments = route.split('/').filter(Boolean);
  return segments.map(segment => {
    // For URLs with already encoded characters, we need to handle them specially
    if (segment === 'bashk%C3%ABpun%C3%ABtor%C3%ABt' && toLang === 'en') {
      return 'networking';
    }
    
    // Standard translation lookup
    return translations[toLang]?.routes[segment] || segment;
  }).join('/');
};

// Helper function to get proper menu display text
export const getMenuText = (key, language) => {
  return translations[language]?.menu[key] || key;
};

export const getRouteLanguage = (pathname) => {
  const lang = pathname.split('/')[1];
  return ['en', 'sq'].includes(lang) ? lang : 'en';
};