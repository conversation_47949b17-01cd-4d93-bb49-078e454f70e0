/* AICRelationshipPage.css */

.relationshipwithministers-page-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  padding: 2rem 0;
}

.relationshipwithministers-main-content {
  max-width: 1600px;
  width: 100%;
  display: flex;
  padding: 0 2rem;
}

.relationshipwithministers-content-wrapper {
  display: flex;
  width: 100%;
  gap: 3rem;
}

.relationshipwithministers-content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.relationshipwithministers-content-title {
  font-size: 2.75rem;
  font-weight: bold;
  color: #1a202c;
  margin-bottom: 1.5rem;
  border-bottom: 3px solid #e41e26;
  padding-bottom: 0.75rem;
}

.relationshipwithministers-main-image {
  margin: 1.5rem 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 50vh;
  overflow: hidden;
  border-radius: 1rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.relationshipwithministers-main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.relationshipwithministers-content-text {
  color: #2d3748;
  line-height: 1.8;
  font-size: 1.25rem;
}

.relationshipwithministers-content-text p {
  margin-bottom: 1.5rem;
}

.relationshipwithministers-sidebar {
  width: 400px;
  padding-left: 2rem;
  border-left: 2px solid #e2e8f0;
}

.relationshipwithministers-sidebar-nav {
  margin-bottom: 2rem;
}

.relationshipwithministers-sidebar-title {
  font-size: 1.75rem;
  font-weight: bold;
  color: #1a202c;
  margin-bottom: 1.5rem;
  text-transform: uppercase;
}

.relationshipwithministers-sidebar-buttons {
  list-style-type: none;
  padding: 0;
}

.relationshipwithministers-sidebar-button {
  display: block;
  padding: 1rem 1.25rem;
  margin-bottom: 1rem;
  cursor: pointer;
  background-color: #edf2f7;
  border-radius: 0.75rem;
  transition: all 0.3s ease-in-out;
  font-size: 1.125rem;
  color: #4a5568;
  text-decoration: none;
  font-weight: 500;
}

.relationshipwithministers-sidebar-button:hover {
  background-color: #e2e8f0;
  transform: translateY(-2px);
}

.relationshipwithministers-sidebar-button.active {
  background-color: #e41e26;
  color: white;
  box-shadow: 0 4px 6px rgba(220, 38, 38, 0.2);
}

.relationshipwithministers-project-section {
  margin-top: 2rem;
}

.relationshipwithministers-project-title {
  font-size: 1.75rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  color: #1a202c;
  text-transform: uppercase;
}

.relationshipwithministers-project-carousel {
  background-color: #2a4365;
  color: white;
  padding: 1.5rem;
  border-radius: 1rem;
  margin-bottom: 2rem;
  overflow: hidden;
}

.relationshipwithministers-project-card {
  display: flex;
  flex-direction: column;
}

.relationshipwithministers-project-image {
  width: 100%;
  border-radius: 0.75rem;
  margin-bottom: 1.25rem;
  height: 200px;
  object-fit: cover;
}

.relationshipwithministers-project-card-content h4 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.relationshipwithministers-project-card-content p {
  font-size: 1.125rem;
  line-height: 1.6;
  margin-bottom: 1rem;
  max-height: 5.4em;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.relationshipwithministers-project-navigation {
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
}

.relationshipwithministers-nav-arrow {
  cursor: pointer;
  transition: color 0.3s ease, transform 0.3s ease;
}

.relationshipwithministers-nav-arrow:hover {
  color: #e41e26;
  transform: scale(1.1);
}

.relationshipwithministers-read-more {
  display: inline-block;
  color: #e41e26;
  font-weight: bold;
  text-decoration: none;
  font-size: 1.125rem;
  transition: color 0.3s ease;
  background-color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
}

.relationshipwithministers-read-more:hover {
  color: #e41e26;
  text-decoration: underline;
}

@media (max-width: 1650px) {
  .relationshipwithministers-content-wrapper {
    gap: 2rem;
  }

  .relationshipwithministers-sidebar {
    width: 350px;
  }
}

@media (max-width: 1366px) {
  .relationshipwithministers-content-wrapper {
    flex-direction: column;
  }

  .relationshipwithministers-sidebar {
    width: 100%;
    padding-left: 0;
    border-left: none;
    margin-top: 2rem;
  }
}

@media (max-width: 768px) {
  .relationshipwithministers-main-content {
    padding: 1rem;
  }

  .relationshipwithministers-content-title {
    font-size: 2.25rem;
  }

  .relationshipwithministers-main-image {
    height: 40vh;
  }

  .relationshipwithministers-content-text {
    font-size: 1.125rem;
  }

  .relationshipwithministers-sidebar-button {
    padding: 0.875rem 1.125rem;
    font-size: 1rem;
  }

  .relationshipwithministers-project-card-content h4 {
    font-size: 1.25rem;
  }

  .relationshipwithministers-project-card-content p {
    font-size: 1rem;
  }
}