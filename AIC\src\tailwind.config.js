/** @type {import('tailwindcss').Config} */
module.exports = {
  mode: 'jit',
  content: [
    './src/**/*.{js,jsx,ts,tsx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        corinthiago: ['Corinthiago', 'serif'],
      },
      keyframes: {
        spotlight: {
          '0%': { opacity: 0, transform: 'scale(0.8)' },
          '100%': { opacity: 1, transform: 'scale(1)' },
        }
      },
      animation: {
        spotlight: 'spotlight 0.2s ease-out forwards',
      },
    },
  },
  plugins: [],
}