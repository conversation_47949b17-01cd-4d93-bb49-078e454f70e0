{"name": "aic", "private": true, "version": "1.0.0", "main": "package.json", "license": "UNLICENSED", "scripts": {"dev": "sanity dev", "start": "sanity start", "build": "sanity build", "deploy": "sanity deploy", "deploy-graphql": "sanity graphql deploy"}, "keywords": ["sanity"], "dependencies": {"@sanity/client": "^6.29.1", "@sanity/icons": "^3.7.0", "@sanity/ui": "^2.15.13", "@sanity/vision": "^3.60.0", "dotenv": "^16.4.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "sanity": "^3.60.0", "styled-components": "^6.1.8", "ts-node": "^10.9.2"}, "devDependencies": {"@sanity/eslint-config-studio": "^4.0.0", "@types/react": "^18.0.25", "eslint": "^8.6.0", "prettier": "^3.0.2", "typescript": "^5.8.3"}, "prettier": {"semi": false, "printWidth": 100, "bracketSpacing": false, "singleQuote": true}}