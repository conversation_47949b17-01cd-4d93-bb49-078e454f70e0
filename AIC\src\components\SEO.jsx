// components/SEO.jsx
import React from 'react';
import { Helmet } from 'react-helmet-async';

const SEO = ({ 
  post,
  postType,
  customTitle,
  customDescription,
  ogImage, 
  ogUrl, 
  ogType = 'article',
  language = 'en'
}) => {
  // Generate title - use custom title first, then post metaTitle, then post title
  const title = customTitle || post?.metaTitle || post?.title || post?.jobTitle || 'Page Title';
  
  // Generate description - use custom description first, then post metaDescription
  let description = customDescription || post?.metaDescription;
  
  // If no description, try to generate one from post content
  if (!description && post?.body && Array.isArray(post.body)) {
    // Extract text from Sanity block content
    const textBlocks = post.body
      .filter(block => block._type === 'block' && block.children)
      .map(block => 
        block.children
          .filter(child => child._type === 'span')
          .map(child => child.text)
          .join('')
      )
      .join(' ');
    
    // Create description from first 150 characters
    if (textBlocks.length > 0) {
      description = textBlocks.slice(0, 150).trim() + '...';
    }
  }
  
  // Final fallback
  if (!description) {
    description = language === 'en' 
      ? 'Learn more about our work and initiatives.' 
      : 'Mësoni më shumë rreth punës dhe nismave tona.';
  }
  
  // Ensure description is within limits
  if (description.length > 160) {
    description = description.slice(0, 157) + '...';
  }

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="language" content={language} />
      <meta name="robots" content="index, follow" />
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={ogType} />
      {ogUrl && <meta property="og:url" content={ogUrl} />}
      {ogImage && <meta property="og:image" content={ogImage} />}
      {ogImage && <meta property="og:image:width" content="1200" />}
      {ogImage && <meta property="og:image:height" content="630" />}
      <meta property="og:locale" content={language === 'sq' ? 'sq_AL' : 'en_US'} />
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      {ogImage && <meta name="twitter:image" content={ogImage} />}
      
      {/* Additional Meta Tags */}
      {post?.publishDate && <meta property="article:published_time" content={post.publishDate} />}
      {post?.projectDate && <meta property="article:published_time" content={post.projectDate} />}
      
      {/* Canonical URL */}
      {ogUrl && <link rel="canonical" href={ogUrl} />}
      
      {/* Language alternates */}
      <meta property="og:locale:alternate" content={language === 'sq' ? 'en_US' : 'sq_AL'} />
    </Helmet>
  );
};

export default SEO;