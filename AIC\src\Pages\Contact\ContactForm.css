.ContactForm {
    max-width: 1200px;
    margin: 40px auto;
    font-family: Arial, sans-serif;
    background-color: #f5f5f5;
    padding: 30px;
    border-radius: 15px;
}

.ContactForm-map {
    padding-top: 50px;
    width: 100%;
    height: 500px;
    overflow: hidden;
    margin-bottom: 30px;
    border-radius: 15px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.ContactForm-map img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.ContactForm-content {
    display: flex;
    gap: 60px;
}

.ContactForm-info {
    flex: 1;
    padding-top: 20px;
}

.ContactForm-info h2 {
    font-size: 18px;
    color: #333;
    margin-bottom: 5px;
}

.ContactForm-info h3 {
    font-size: 28px;
    color: #ff0000;
    margin-bottom: 25px;
}

.ContactForm-infoItem {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    color: #666;
}

.ContactForm-infoItem i {
    margin-right: 12px;
    font-size: 20px;
    color: #ff0000;
}

.ContactForm-form {
    flex: 2;
    display: flex;
    flex-direction: column;
    gap: 20px; /* Increased gap between form sections */
}

.ContactForm-formRow {
    display: flex;
    gap: 20px;
    margin-bottom:5px; /* Add margin-bottom to create vertical space between rows */
}

.ContactForm input,
.ContactForm select,
.ContactForm textarea {
    width: 100%;
    padding: 15px 20px;
    border: 1px solid #e0e0e0;
    border-radius: 25px;
    font-size: 16px;
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    margin-bottom: 20px; /* Added margin-bottom for vertical spacing */
}

.ContactForm input:focus,
.ContactForm select:focus,
.ContactForm textarea:focus {
    outline: none;
    border-color: #ff0000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.ContactForm textarea {
    height: 150px;
    resize: vertical;
    border-radius: 20px;
}

.submit-button {
    background-color: #ff0000;
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    align-self: flex-start;
    border-radius: 25px;
    box-shadow: 0 4px 8px rgba(255, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.submit-button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
}

.submit-button:focus:not(:active)::after {
    animation: ripple 1s ease-out;
}

.submit-button.sending {
    background-color: #ffa500;
    animation: pulse 1.5s infinite;
}

.submit-button.success {
    background-color: #4CAF50;
    animation: success-bounce 0.5s;
}

.submit-button.error {
    background-color: #f44336;
    animation: error-shake 0.5s;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    20% {
        transform: scale(25, 25);
        opacity: 0.5;
    }
    100% {
        opacity: 0;
        transform: scale(40, 40);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes success-bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes error-shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

/* Icon placeholders */
.ContactForm-iconClock::before { content: "🕒"; }
.ContactForm-iconEmail::before { content: "✉️"; }
.ContactForm-iconLocation::before { content: "📍"; }

/* Responsive design */
@media (max-width: 768px) {
    .ContactForm-content {
        flex-direction: column;
    }
    
    .ContactForm-formRow {
        flex-direction: column;
        gap: 15px;
    }
    
    .ContactForm {
        padding: 20px;
    }

    .ContactForm-form {
        gap: 15px; /* Adjusted gap for mobile view */
    }
}
