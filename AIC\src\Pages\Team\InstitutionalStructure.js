import React from 'react';
import { useLanguage } from '../../components/languageUtils';
import SEO from '../../components/SEO';
import { seoTitles } from '../../config/seoTitles';

const MemberCard = ({ name }) => {
  return (
    <div className="bg-white rounded-lg shadow-md p-6 flex items-center justify-center">
      <span className="text-xl font-semibold text-gray-800 text-center">{name}</span>
    </div>
  );
};

const ResponsiveImagePage = () => {
  const { language } = useLanguage();
  const seoData = seoTitles.institutionalStructure[language];

  const content = {
    en: {
      title: "INSTITUTIONAL STRUCTURE",
      boardTitle: "Advisory Board Members",
      boardMembers: [
        "Mr. <PERSON><PERSON>",
        "Mr. <PERSON><PERSON>",
        "Ms. Sonila Qato",
        "Ms. Llambriola Misto",
        "Mr. <PERSON><PERSON>"
      ],
      imageAlt: "Institutional Structure"
    },
    sq: {
      title: "STRUKTURA INSTITUCIONALE",
      boardTitle: "Anëtarët e Bordit Këshillimor",
      boardMembers: [
        "Z. Edmir Nufi",
        "Z. Ami <PERSON>zeli",
        "Znj. Sonila Qato",
        "Znj. Llambriola Misto",
        "Z. Valery Tsepkalo"
      ],
      imageAlt: "Struktura Institucionale"
    }
  };

  return (
    <>
      <SEO
        customTitle={seoData.title}
        customDescription={seoData.description}
        language={language}
        ogType="website"
      />
      <div className="container mx-auto px-4 py-8">
      <h1 className="text-4xl font-bold mb-8 text-center text-[#e41e26]">
        {content[language].title}
      </h1>
      
      <div className="flex justify-center mb-12">
        <div className="w-full max-w-4xl">
          <img
            src={language === 'sq' ? "../../Organigrama-01.png" : "../../Organigrama-02.png"}
            alt={content[language].imageAlt}
            className="w-full h-auto object-cover rounded-lg shadow-lg"
            loading="lazy"
          />
        </div>
      </div>
      
      <div className="max-w-3xl mx-auto">
        <h2 className="text-3xl font-semibold mb-6 text-center text-[#e41e26]">
          {content[language].boardTitle}
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {content[language].boardMembers.map((member, index) => (
            <MemberCard key={member} name={member} />
          ))}
        </div>
      </div>
    </div>
    </>
  );
};

export default ResponsiveImagePage;