export default {
  name: 'team<PERSON><PERSON>ber',
  title: 'Team Members/Antaret',
  type: 'document',
  fields: [
    {
      name: 'name',
      title: 'Name',
      type: 'string',
      description: 'Full name of the team member (not displayed for vacant positions)',
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'name',
        maxLength: 96,
      },
      description: 'Used for URL paths to individual profiles',
    },
    {
      name: 'isVacant',
      title: 'Is Vacant Position',
      type: 'boolean',
      description: 'Check this if the position is vacant/available',
      initialValue: false
    },
    {
      name: 'positionTitle',
      title: 'Position Title',
      type: 'object',
      description: 'Title of the position (will show even if vacant)',
      fields: [
        { name: 'en', type: 'string', title: 'Position Title in English' },
        { name: 'sq', type: 'string', title: 'Position Title in Albanian' }
      ]
    },
    {
      name: 'position',
      title: 'Display Order',
      type: 'number',
      description: 'Use this to control the order of team members (lower numbers appear first)'
    },
    {
      name: 'image',
      title: 'Image',
      type: 'image',
      options: {
        hotspot: true,
      },
      description: 'Optional for vacant positions',
    },
    {
      name: 'role',
      title: 'Role',
      type: 'object',
      fields: [
        { name: 'en', type: 'string', title: 'Role in English' },
        { name: 'sq', type: 'string', title: 'Role in Albanian' }
      ],
      description: 'Current role/title (same as Position Title for most cases)',
    },
    {
      name: 'email',
      title: 'Email',
      type: 'string',
    },
    {
      name: 'linkedin',
      title: 'LinkedIn URL',
      type: 'url',
    },
    {
      name: 'bio',
      title: 'Biography',
      type: 'object',
      fields: [
        {
          name: 'en',
          title: 'English Biography',
          type: 'array',
          of: [{type: 'block'}]
        },
        {
          name: 'sq',
          title: 'Albanian Biography',
          type: 'array',
          of: [{type: 'block'}]
        }
      ]
    },
    {
      name: 'education',
      title: 'Education',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            { 
              name: 'degree', 
              type: 'object', 
              fields: [
                { name: 'en', type: 'string', title: 'Degree in English' },
                { name: 'sq', type: 'string', title: 'Degree in Albanian' }
              ]
            },
            { 
              name: 'school', 
              type: 'object', 
              fields: [
                { name: 'en', type: 'string', title: 'School Name in English' },
                { name: 'sq', type: 'string', title: 'School Name in Albanian' }
              ]
            },
          ],
        },
      ],
    },
    {
      name: 'expertise',
      title: 'Areas of Expertise',
      type: 'object',
      fields: [
        {
          name: 'en',
          title: 'Expertise in English',
          type: 'array',
          of: [{ type: 'string' }]
        },
        {
          name: 'sq',
          title: 'Expertise in Albanian',
          type: 'array',
          of: [{ type: 'string' }]
        }
      ]
    },
    {
      name: 'languages',
      title: 'Languages',
      type: 'object',
      fields: [
        {
          name: 'en',
          title: 'Languages List in English',
          type: 'array',
          of: [{ type: 'string' }]
        },
        {
          name: 'sq',
          title: 'Languages List in Albanian',
          type: 'array',
          of: [{ type: 'string' }]
        }
      ]
    },
    {
      name: 'currentRole',
      title: 'Current Role',
      type: 'object',
      fields: [
        { name: 'en', type: 'string', title: 'Current Role in English' },
        { name: 'sq', type: 'string', title: 'Current Role in Albanian' }
      ]
    },
    {
      name: 'previousRoles',
      title: 'Previous Roles',
      type: 'object',
      fields: [
        {
          name: 'en',
          title: 'Previous Roles in English',
          type: 'array',
          of: [{ type: 'string' }]
        },
        {
          name: 'sq',
          title: 'Previous Roles in Albanian',
          type: 'array',
          of: [{ type: 'string' }]
        }
      ]
    },
  ],
  preview: {
    select: {
      title: 'name',
      subtitle: 'role.en',
      isVacant: 'isVacant',
      positionTitle: 'positionTitle.en',
      media: 'image'
    },
    // Fix TypeScript error by adding type annotation
    prepare(selection: { title?: string; subtitle?: string; isVacant?: boolean; positionTitle?: string; media?: any }) {
      const { title, subtitle, isVacant, positionTitle, media } = selection;
      return {
        title: isVacant ? `[VACANT] ${positionTitle || ''}` : title,
        subtitle: subtitle || positionTitle || '',
        media: media
      };
    }
  }
}