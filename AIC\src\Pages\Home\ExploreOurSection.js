import React, { useState, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useLanguage } from '../../components/languageUtils';

const ExploreOurProjects = () => {
  const { language } = useLanguage();
  const navigate = useNavigate();
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [cursorVisible, setCursorVisible] = useState(false);
  const containerRef = useRef(null);
  const [isHovering, setIsHovering] = useState(false);

  // Category data from ProfessionalProjectCategories component
  const categories = {
    en: [
      { 
        id: 'strategic-projects', 
        name: 'Strategic Projects', 
        route: 'strategic-projects', 
        image: "/image1.jpeg"
      },
      { 
        id: 'revitalization', 
        name: 'Revitalization & Urban Renewal', 
        route: 'revitalization', 
        image: "/image2.jpeg"
      },
      { 
        id: 'real-estate', 
        name: 'Real Estate Development',
        route: 'real-estate',
        image: "/image3.jpeg"
      }
    ],
    sq: [
      { 
        id: 'strategic-projects', 
        name: 'Projekte Strategjike', 
        route: 'projekte-strategjike', 
        image: "/image1.jpeg"
      },
      { 
        id: 'revitalization', 
        name: 'Rindërtim dhe Zhvillim i ri Urban',
        route: 'rijetezimi', 
        image: "/image2.jpeg"
      },
      { 
        id: 'real-estate', 
        name: 'Kompleksi i Sherbimeve Publike',
        route: 'projekte-te-pasurive',
        image: "/image3.jpeg"
      }
    ]
  };

  const handleMouseMove = (e) => {
    if (containerRef.current) {
      const rect = containerRef.current.getBoundingClientRect();
      setMousePosition({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
    }
    setCursorVisible(true);
  };

  const handleMouseLeave = () => {
    setCursorVisible(false);
  };

  const getProjectsPath = (lang) => {
    return lang === 'sq' ? 'projekte' : 'projects';
  };

  const handleCategoryClick = (categoryId) => {
    const category = categories[language].find(cat => cat.id === categoryId);
    if (category) {
      const projectsPrefix = language === 'en' ? 'projects' : 'projekte';
      navigate(`/${language}/${projectsPrefix}/${category.route}`);
    }
  };

  // Custom keyframes animation for the enhancedShine effect from paste-3
  const enhancedShineKeyframes = `
    @keyframes enhancedShine {
      0%, 100% { filter: brightness(1.2) drop-shadow(0 0 15px rgba(221, 30, 38, 0.6)); opacity: 0.2; }
      25% { filter: brightness(1.2) drop-shadow(0 0 25px rgba(221, 30, 38, 0.8)); opacity: 0.4; }
      50% { filter: brightness(1.3) drop-shadow(0 0 35px rgba(221, 30, 38, 1)); opacity: 0.6; }
      75% { filter: brightness(1.2) drop-shadow(0 0 25px rgba(221, 30, 38, 0.8)); opacity: 0.4; }
    }
    .animate-enhanced-shine { animation: enhancedShine 6s infinite; }
    
    @keyframes float-3d {
      0%, 100% { transform: translateY(0) translateX(-50%); }
      50% { transform: translateY(-10px) translateX(-50%); }
    }
    .animate-float-3d {
      animation: float-3d 8s ease-in-out infinite;
    }
    
    @keyframes float-3d-right {
      0%, 100% { transform: translateY(0) translateX(50%) scaleX(-1); }
      50% { transform: translateY(-10px) translateX(50%) scaleX(-1); }
    }
    .animate-float-3d-right {
      animation: float-3d-right 8s ease-in-out infinite;
    }
  `;

  return (
    <div 
      ref={containerRef}
      className="relative w-full min-h-screen bg-gray-50 overflow-hidden"
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
    >
      {/* Custom animation styles */}
      <style>{enhancedShineKeyframes}</style>

      {/* Background Elements - from paste-2 */}
      {cursorVisible && (
        <>
          <div
            className="pointer-events-none absolute -translate-x-1/2 -translate-y-1/2 h-[600px] w-[600px] bg-gradient-to-r from-red-500/10 to-red-600/10 blur-3xl rounded-full transition-transform duration-200"
            style={{
              left: mousePosition.x,
              top: mousePosition.y,
              transform: `translate(-50%, -50%) scale(${isHovering ? 1.2 : 1})`,
            }}
          />
          <div
            className="pointer-events-none absolute -translate-x-1/2 -translate-y-1/2 h-[300px] w-[300px] bg-gradient-to-r from-red-400/20 to-red-500/20 blur-2xl rounded-full transition-transform duration-150"
            style={{
              left: mousePosition.x,
              top: mousePosition.y,
              transform: `translate(-50%, -50%) scale(${isHovering ? 0.8 : 1})`,
            }}
          />
        </>
      )}

      {/* Background images with enhanced shine effect - combining paste-2 and paste-3 */}
      <div className="absolute bottom-0 left-0 w-full h-full overflow-hidden z-0 group">
        <img 
          src="/Asset 2.png" 
          alt="" 
          className="absolute bottom-0 left-0 w-1/4 h-auto object-cover opacity-70 transform -translate-x-1/2 transition-transform duration-500 hover:scale-110 group-hover:animate-enhanced-shine animate-float-3d" 
        />
        <img 
          src="/Asset 2.png" 
          alt="" 
          className="absolute bottom-0 right-0 w-1/4 h-auto object-cover opacity-70 transform translate-x-1/2 scale-x-[-1] transition-transform duration-500 hover:scale-110 group-hover:animate-enhanced-shine animate-float-3d-right"
          style={{ animationDelay: '0.5s' }}
        />
      </div>

      {/* Interactive grid background - from paste-2 */}
      <div className="absolute inset-0">
        <div 
          className="absolute inset-0"
          style={{
            backgroundImage: 'radial-gradient(circle at center, rgba(220,38,38,0.1) 1px, transparent 1px)',
            backgroundSize: '40px 40px',
            backgroundPosition: `${mousePosition.x * 0.02}px ${mousePosition.y * 0.02}px`,
            transition: 'background-position 0.15s ease-out',
          }}
        />
        <div 
          className="absolute inset-0"
          style={{
            backgroundImage: 'radial-gradient(circle at center, rgba(220,38,38,0.05) 1.5px, transparent 1.5px)',
            backgroundSize: '25px 25px',
            backgroundPosition: `${mousePosition.x * 0.01}px ${mousePosition.y * 0.01}px`,
            transition: 'background-position 0.25s ease-out',
          }}
        />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Header section - from paste-2 */}
        <div className="text-center mb-16 relative">
          <div className="inline-block relative">
            <h2 className="text-red-600 text-lg font-medium uppercase tracking-wider mb-2 relative z-10 group">
              <span className="relative">
                {language === 'en' ? 'OUR PROJECTS' : 'PROJEKTET TONA'}
                <div className="absolute -bottom-1 left-0 w-full h-0.5 bg-red-600 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
              </span>
            </h2>
            <h2 className="text-5xl font-bold text-gray-900 uppercase tracking-wide relative z-10">
              {language === 'en' ? 'DISCOVER OUR SECTORS' : 'ZBULONI SEKTORËT TANË'}
            </h2>
            <div className="absolute -inset-x-20 -inset-y-10 bg-red-50 opacity-50 blur-3xl -z-10"></div>
          </div>
        </div>

        {/* Project grid - combining paste-1, paste-2, and paste-3 styles */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {categories[language].map((category) => (
            <div 
              key={category.id}
              className="group relative h-80 rounded-lg overflow-hidden cursor-pointer transition-all duration-500 border-2 border-red-600 hover:scale-105 hover:-rotate-1 hover:shadow-[0_0_20px_rgba(221,30,38,0.7)]"
              onClick={() => handleCategoryClick(category.id)}
              onMouseEnter={() => setIsHovering(true)}
              onMouseLeave={() => setIsHovering(false)}
            >
              {/* Card image */}
              <img 
                src={category.image} 
                alt={category.name} 
                className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
              />
              
              {/* Default overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent">
                {/* Hover overlay */}
                <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                  <div className="absolute inset-0 bg-gradient-to-r from-red-500/30 to-red-600/30" />
                </div>
              </div>

              {/* Card content */}
              <div className="absolute bottom-0 left-0 right-0 p-6 transform transition-transform duration-500">
                <h3 className="text-white text-2xl font-bold mb-2 transform transition-all duration-500 group-hover:translate-x-2">
                  {category.name}
                </h3>
                <div className="h-0.5 w-16 bg-red-500 transform origin-left scale-x-0 transition-transform duration-500 group-hover:scale-x-100" />
                
                {/* Description - shows on hover */}
                <div className="max-w-[70%] mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                  <p className="text-white text-sm">
                    {category.description}
                  </p>
                </div>
              </div>

              {/* Decorative elements - from paste-2 */}
              <div className="absolute right-0 bottom-0 transform translate-x-1/2 translate-y-1/2 pointer-events-none transition-transform duration-500 group-hover:rotate-12">
                <img src="/Asset 1.png" alt="" className="opacity-80 transition-all duration-300 group-hover:filter group-hover:drop-shadow-[0_0_10px_rgba(221,30,38,0.2)]" />
              </div>
              <div className="absolute left-0 bottom-0 transform -translate-x-1/2 translate-y-1/2 pointer-events-none transition-transform duration-500 group-hover:-rotate-12">
                <img src="/Asset 1.png" alt="" className="opacity-80 transition-all duration-300 group-hover:filter group-hover:drop-shadow-[0_0_10px_rgba(221,30,38,0.2)]" />
              </div>
            </div>
          ))}
        </div>

        {/* View Projects button - from paste-2 */}
        <div className="text-center">
          <Link 
            to={`/${language}/${getProjectsPath(language)}`}
            className="relative inline-block px-8 py-4 bg-red-600 text-white font-bold uppercase rounded-xl transform transition-all duration-300 hover:scale-105 hover:bg-red-700 group"
            onMouseEnter={() => setIsHovering(true)}
            onMouseLeave={() => setIsHovering(false)}
          >
            <span className="relative z-10">
              {language === 'en' ? 'VIEW PROJECTS' : 'SHIKO PROJEKTET'}
            </span>
            <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-red-500 to-red-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
            <div className="absolute -inset-1 rounded-xl bg-gradient-to-r from-red-500 to-red-600 opacity-0 blur group-hover:opacity-30 transition-opacity duration-300 -z-10"></div>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ExploreOurProjects;