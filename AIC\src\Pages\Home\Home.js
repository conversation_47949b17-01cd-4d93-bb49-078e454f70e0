// src/App.js
import React from 'react';
import { useLanguage } from '../../components/languageUtils';
import SEO from '../../components/SEO';
import { seoTitles } from '../../config/seoTitles';
import HeaderHome from './HeaderHome';
import Redbox from "./Redbox";
import Section from "./Section";
import Project from "./Project";
import ExploreOurSection from "./ExploreOurSection";
import News from "./News";
import Pin from "./Pin";
import HomeTeam from "./HomeTeam";
import Logo from "./Logo";
import JoinUsSection from "./JoinusSection"
import HeroSection from "./HeroSection"


function Home() {
  const { language } = useLanguage();
  const seoData = seoTitles.home[language];

  return (
    <>
      <SEO
        customTitle={seoData.title}
        customDescription={seoData.description}
        language={language}
        ogType="website"
      />
      <div className="App">
        <HeroSection/>
        <Redbox/>
        <Section />
        <Project />
        <ExploreOurSection/>
        <News/>
        <Pin/>
        <HomeTeam/>
        <JoinUsSection/>
        <Logo/>
      </div>
    </>
  );
}

export default Home;