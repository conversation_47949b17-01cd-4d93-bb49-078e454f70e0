import { defineField, defineType } from 'sanity';

// Schema for categories and documents
export const lawpdfCategorySchema = defineType({
  name: 'lawpdfCategory',
  title: 'Law & Regulation Category',
  type: 'document',
  fields: [
    defineField({
      name: 'title_en',
      title: 'Category Title (English)',
      type: 'string',
    }),
    defineField({
      name: 'title_sq',
      title: 'Category Title (Albanian)',
      type: 'string',
    }),
    defineField({
      name: 'isClickable',
      title: 'Is Category Clickable',
      description: 'If enabled, this category title will be blue and clickable',
      type: 'boolean',
      initialValue: false,
    }),
    defineField({
      name: 'sortOrder',
      title: 'Sort Order',
      description: 'Controls the display order (lower numbers appear first)',
      type: 'number',
      initialValue: 10,
    }),
    defineField({
      name: 'subcategories',
      title: 'Subcategories',
      type: 'array',
      of: [{ type: 'reference', to: [{ type: 'lawpdfSubcategory' }] }],
    }),
    defineField({
      name: 'documents',
      title: 'Documents (direct in this category)',
      description: 'Add documents directly to this category (not in subcategories)',
      type: 'array',
      of: [{ type: 'reference', to: [{ type: 'lawpdfItem' }] }],
    }),
  ],
  preview: {
    select: {
      title_sq: 'title_sq',
      title_en: 'title_en',
    },
    prepare({ title_sq, title_en }) {
      return {
        title: title_sq || title_en,
        subtitle: 'Law & Regulation Category',
      };
    },
  },
});

// Schema for subcategories
export const lawpdfSubcategorySchema = defineType({
  name: 'lawpdfSubcategory',
  title: 'Law & Regulation Subcategory',
  type: 'document',
  fields: [
    defineField({
      name: 'title_en',
      title: 'Subcategory Title (English)',
      type: 'string',
    }),
    defineField({
      name: 'title_sq',
      title: 'Subcategory Title (Albanian)',
      type: 'string',
    }),
    defineField({
      name: 'isClickable',
      title: 'Is Subcategory Clickable',
      description: 'If enabled, this subcategory title will be blue and clickable',
      type: 'boolean',
      initialValue: true,
    }),
    defineField({
      name: 'sortOrder',
      title: 'Sort Order',
      description: 'Controls the display order (lower numbers appear first)',
      type: 'number',
      initialValue: 10,
    }),
    defineField({
      name: 'tertiaryCategories',
      title: 'Tertiary Categories',
      type: 'array',
      of: [{ type: 'reference', to: [{ type: 'lawpdfTertiaryCategory' }] }],
    }),
    defineField({
      name: 'documents',
      title: 'Documents (direct in this subcategory)',
      description: 'Add documents directly to this subcategory',
      type: 'array',
      of: [{ type: 'reference', to: [{ type: 'lawpdfItem' }] }],
    }),
  ],
  preview: {
    select: {
      title_sq: 'title_sq',
      title_en: 'title_en',
    },
    prepare({ title_sq, title_en }) {
      return {
        title: title_sq || title_en,
        subtitle: 'Law & Regulation Subcategory',
      };
    },
  },
});

// Schema for tertiary categories (third level)
export const lawpdfTertiaryCategorySchema = defineType({
  name: 'lawpdfTertiaryCategory',
  title: 'Law & Regulation Tertiary Category',
  type: 'document',
  fields: [
    defineField({
      name: 'title_en',
      title: 'Tertiary Category Title (English)',
      type: 'string',
    }),
    defineField({
      name: 'title_sq',
      title: 'Tertiary Category Title (Albanian)',
      type: 'string',
    }),
    defineField({
      name: 'isClickable',
      title: 'Is Tertiary Category Clickable',
      description: 'If enabled, this tertiary category title will be blue and clickable',
      type: 'boolean',
      initialValue: true,
    }),
    defineField({
      name: 'sortOrder',
      title: 'Sort Order',
      description: 'Controls the display order (lower numbers appear first)',
      type: 'number',
      initialValue: 10,
    }),
    defineField({
      name: 'documents',
      title: 'Documents',
      type: 'array',
      of: [{ type: 'reference', to: [{ type: 'lawpdfItem' }] }],
    }),
  ],
  preview: {
    select: {
      title_sq: 'title_sq',
      title_en: 'title_en',
    },
    prepare({ title_sq, title_en }) {
      return {
        title: title_sq || title_en,
        subtitle: 'Law & Regulation Tertiary Category',
      };
    },
  },
});

// Schema for individual document items
export const lawpdfItemSchema = defineType({
  name: 'lawpdfItem',
  title: 'Law & Regulation Document',
  type: 'document',
  fields: [
    defineField({
      name: 'title_en',
      title: 'Document Title (English)',
      type: 'string',
    }),
    defineField({
      name: 'title_sq',
      title: 'Document Title (Albanian)',
      type: 'string',
    }),
    defineField({
      name: 'file_en',
      title: 'Document File (English)',
      type: 'file',
      options: {
        accept: 'application/pdf',
      },
    }),
    defineField({
      name: 'file_sq',
      title: 'Document File (Albanian)',
      type: 'file',
      options: {
        accept: 'application/pdf',
      },
    }),
    defineField({
      name: 'sortOrder',
      title: 'Sort Order',
      description: 'Controls the display order (lower numbers appear first)',
      type: 'number',
      initialValue: 10,
    }),
  ],
  preview: {
    select: {
      title_sq: 'title_sq',
      title_en: 'title_en',
      file_sq: 'file_sq.asset.originalFilename',
      file_en: 'file_en.asset.originalFilename',
    },
    prepare({ title_sq, title_en, file_sq, file_en }) {
      return {
        title: title_sq || title_en,
        subtitle: file_sq || file_en,
        media: 'file_sq',
      };
    },
  },
});