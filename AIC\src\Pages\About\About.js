// src/App.js
import React from 'react';
import { useLanguage } from '../../components/languageUtils';
import SEO from '../../components/SEO';
import { seoTitles } from '../../config/seoTitles';
import MissionSection from './MissoinSection';
import PinAboutUs from './PinAboutUs';
import ImpactSection from './ImpactSection';

import './About.css';


function About() {
  const { language } = useLanguage();
  const seoData = seoTitles.about[language];

  return (
    <>
      <SEO
        customTitle={seoData.title}
        customDescription={seoData.description}
        language={language}
        ogType="website"
      />
      <div className="App">
        <MissionSection/>
        <PinAboutUs/>
        <ImpactSection/>
      </div>
    </>
  );
}

export default About;