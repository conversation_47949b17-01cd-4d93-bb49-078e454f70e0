// migrations/categoryToArray.js
import { createClient } from '@sanity/client'
// Create a client using your project config
const client = createClient({
    projectId: '1ag8k8v1',
    dataset: 'production2',
    apiVersion: '2021-06-07',
    useCdn: false,
    token: 'skjcWDDiiPKF3m2s6kXljHSQe1xOz2WDATgJd5KQ20QcQi2pUfvs7sgxugmZhWeffQsHL7S6LCgjVsPLy' // Your token from debug output
  })

async function migrateCategories() {
  try {
    console.log('Starting migration...')
    // Fetch all project posts with a category field
    const documents = await client.fetch(
      `*[_type == "projectsPost" && defined(category)]`
    )
    
    console.log(`Found ${documents.length} documents with category field`)
    
    // Filter for documents where category is not an array
    const docsToMigrate = documents.filter(doc => !Array.isArray(doc.category))
    
    console.log(`Found ${docsToMigrate.length} documents to migrate`)
    
    // Process each document
    let migrated = 0
    for (const doc of docsToMigrate) {
      try {
        console.log(`Migrating document: ${doc.title || 'Untitled'} (${doc._id})`)
        // Convert string to array
        await client
          .patch(doc._id)
          .set({ 
            category: doc.category ? [doc.category] : [] 
          })
          .commit()
        
        migrated++
        console.log(`Successfully migrated (${migrated}/${docsToMigrate.length})`)
      } catch (err) {
        console.error(`Failed to migrate document ${doc._id}:`, err)
      }
    }
    
    console.log(`Migration completed: ${migrated} documents updated`)
  } catch (err) {
    console.error('Migration failed:', err)
  }
}

// Run the migration
migrateCategories()