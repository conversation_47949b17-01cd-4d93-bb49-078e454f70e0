.team-members-container {
    margin: 0 auto;
    background-color: #f3f4f6;
    background-image: url('/public/Asset 3.png');  
    background-size: 300px 300px;
    background-position: top;
    background-repeat: no-repeat;
    background-origin: content-box;
    padding-top: 50px;
}

.team-subheading {
    text-align: center;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #4b5563;
    font-family: 'Playfair Display', serif;
}

.team-heading {
    text-align: center;
    font-size: 2.25rem;
    font-weight: bold;
    color: #dc2626;
    margin-bottom: 4rem;
    font-family: 'Playfair Display', serif;
}

.team-members {
    display: flex;
    justify-content: center;
}

.team-member-container {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    justify-content: center;
    margin-bottom: 20px;
    margin-top: 10px;
    padding: 0 1rem;
}

.team-member-card {
    position: relative;
    border-radius: 2rem;
    overflow: hidden;
    width: 18rem;
    height: 23rem;
    background-color: white;
    box-shadow: 0 8px 10px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    margin-bottom: 2rem;
    display: flex;
    flex-direction: column;
}

.team-member-card:hover {
    background-color: #dc2626;
    box-shadow: 0 14px 20px rgba(220, 38, 38, 0.35);
    transform: translateY(-10px);
}

.team-card-content {
    padding: 2rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    text-align: center;
}

.team-image-container {
    width: 10rem;
    height: 10rem;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 1rem;
    border: 4px solid #dc2626;
    transition: border 0.3s ease;
}

.team-member-card:hover .team-image-container {
    border: 5px solid white;
}

.team-member-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.team-placeholder-image {
    width: 100%;
    height: 100%;
    background-color: #d1d5db;
    display: flex;
    align-items: center;
    justify-content: center;
}

.team-member-name {
    font-size: 1.55rem;
    font-weight: bold;
    color: black;
    transition: color 0.3s ease;
    font-family: 'Playfair Display', serif;
    margin-bottom: 0.5rem;
}

.team-member-card:hover .team-member-name {
    color: white;
}

.team-member-role {
    font-size: 1.1rem;
    color: #4b5563;
    transition: color 0.3s ease;
    font-family: 'Playfair Display', serif;
    margin-top: 0.5rem;
    font-weight: 500;
}

.team-member-card:hover .team-member-role {
    color: white;
}

/* Responsive Styles */
@media (max-width: 1440px) {
    .team-member-container {
        max-width: 1200px;
        margin-left: auto;
        margin-right: auto;
    }
    .team-member-card {
        width: calc(33.33% - 1.33rem);
    }
}

@media (max-width: 1200px) {
    .team-member-card {
        width: calc(50% - 1rem);
    }
}

@media (max-width: 992px) {
    .team-member-card {
        width: calc(50% - 1rem);
    }
}
@media (max-width: 768px) {
    .team-member-container {
        gap: 1rem;
    }
    .team-member-card {
        width: calc(50% - 0.5rem);
        height: auto;  /* Changed from fixed height to auto */
        min-height: 10rem;  /* Added min-height for consistency */
    }
    .team-card-content {
        padding: 1rem;
    }
    .team-image-container {
        width: 6rem;
        height: 6rem;
        margin-bottom: 0.75rem;
    }
    .team-member-name {
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }
    .team-member-role {
        font-size: 0.875rem;
    }
}

@media (max-width: 480px) {
    .team-members-container {
        padding-top: 30px;
    }
    .team-heading {
        font-size: 1.5rem;
        margin-bottom: 2rem;
    }
    .team-subheading {
        font-size: 0.875rem;
    }
    .team-member-container {
        gap: 1rem;
    }
    .team-member-card {
        width: 100%;
        max-width: 300px;
        border-radius: 1rem;
        height: auto;
        min-height: 10rem;  /* Reduced min-height */
    }
    .team-card-content {
        padding: 1rem;
    }
    .team-image-container {
        width: 5rem;
        height: 5rem;
        margin-bottom: 0.5rem;
    }
    .team-member-name {
        font-size: 1rem;
    }
    .team-member-role {
        font-size: 0.875rem;
    }
}

@media (max-width: 350px) {
    .team-member-card {
        min-height: auto;  /* Remove min-height for very small screens */
    }
    .team-image-container {
        width: 4rem;
        height: 4rem;
    }
    .team-member-name {
        font-size: 0.9rem;
    }
    .team-member-role {
        font-size: 0.8rem;
    }
}