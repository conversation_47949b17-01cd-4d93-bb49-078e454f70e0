import { defineConfig } from 'sanity';
import { deskTool } from 'sanity/desk';
import { visionTool } from '@sanity/vision';
import { schemaTypes } from './schemaTypes';
import { myStructure } from './src/sanity/customizations/deskStructure';
import { StudioBrand } from './src/sanity/customizations/StudioLogo';

export default defineConfig({
  name: 'default',
  title: 'AIC Admin - Production2',
  projectId: '1ag8k8v1',
  dataset: 'production2',
  
  plugins: [
    deskTool({
      structure: myStructure
    }), 
    visionTool()
  ],
  
  schema: {
    types: schemaTypes,
  },
  
  studio: {
    components: {
      logo: <PERSON>B<PERSON>
    }
  }
});