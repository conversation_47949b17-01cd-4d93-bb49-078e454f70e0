import { createClient } from '@sanity/client';

// Debug to see if environment variables are being loaded
if (!process.env.REACT_APP_SANITY_PROJECT_ID) {
  console.error('Environment variables not loaded. Check your .env file and restart the development server.');
}

const client = createClient({
  projectId: process.env.REACT_APP_SANITY_PROJECT_ID,
  dataset: process.env.REACT_APP_SANITY_DATASET,
  apiVersion: process.env.REACT_APP_SANITY_API_VERSION,
  useCdn: process.env.REACT_APP_SANITY_USE_CDN === 'true',
});

export default client;