{"name": "shebang-regex", "version": "3.0.0", "description": "Regular expression for matching a shebang line", "license": "MIT", "repository": "sindresorhus/shebang-regex", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["regex", "regexp", "shebang", "match", "test", "line"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}