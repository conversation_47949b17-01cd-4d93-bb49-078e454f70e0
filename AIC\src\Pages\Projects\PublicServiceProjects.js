import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useParams } from 'react-router-dom';
import sanityClient from '../../sanity';
import { useLanguage, translateRoute } from '../../components/languageUtils';

const PublicServiceProjects = () => {
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { language, translations } = useLanguage();
  const { lang } = useParams();
  const navigate = useNavigate();

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const query = `*[
          _type == "projectsPost" && 
          language == $language && 
          isProject == true &&
          "real-estate" in projectCategories &&
          "public-service-complex" in realEstateSubcategories
        ] | order(projectDate desc) {
          title,
          "slug": slug.current,
          mainImage {
            asset-> {
              _id,
              url
            },
            alt
          },
          description,
          projectDate
        }`;

        const result = await sanityClient.fetch(query, { language: language });
        setProjects(result);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching projects:', err);
        setError("Failed to load projects.");
        setLoading(false);
      }
    };

    fetchProjects();
  }, [lang]);

  if (loading) return <div className="text-center py-10">{translations?.common?.loading || 'Loading...'}</div>;
  if (error) return <div className="text-center py-10 text-red-600">{error}</div>;

  const getLocalizedProjectsPrefix = (language) => {
    return language === 'en' ? 'projects' : 'projekte';
  };

  const handleBackClick = () => {
    const projectsPrefix = getLocalizedProjectsPrefix(language);
    const realEstateRoute = translateRoute('real-estate', 'en', language);
    navigate(`/${language}/${projectsPrefix}/${realEstateRoute}`);
  };

  return (
    <div className="container mx-auto px-4 py-12">
      <button 
        onClick={handleBackClick}
        className="flex items-center text-gray-700 hover:text-red-600 transition-colors duration-300 mb-6"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
        </svg>
        {language === 'en' ? 'Back to Real Estate Projects' : 'Kthehu te Projektet e Pasurive të Paluajtshme'}
      </button>

      <div className="bg-white border-b border-gray-200 mb-10">
        <div className="max-w-3xl mx-auto text-center py-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {language === 'en' ? 'Public Service Complex' : 'Kompleksi i Shërbimit Publik'}
          </h1>
          <div className="w-24 h-1 bg-red-600 mx-auto mb-6"></div>
          <p className="text-gray-700 max-w-3xl mx-auto">
            {language === 'en' 
              ? 'Explore our innovative public service developments designed to enhance community infrastructure and provide essential services across Albania.'
              : 'Eksploroni zhvillimet tona inovative të shërbimit publik të projektuara për të përmirësuar infrastrukturën e komunitetit dhe për të ofruar shërbime thelbësore në të gjithë Shqipërinë.'}
          </p>
        </div>
      </div>

      {projects.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project) => {
            const projectsPrefix = getLocalizedProjectsPrefix(language);
            const permalink = `/${language}/${projectsPrefix}/${project.slug}`;
            const projectDate = project.projectDate ? new Date(project.projectDate).toLocaleDateString(language === 'en' ? 'en-US' : 'sq-AL', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            }) : null;

            return (
              <div key={project.slug} className="group bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 flex flex-col">
                <div className="h-56 overflow-hidden relative">
                  {project.mainImage?.asset?.url ? (
                    <img 
                      src={project.mainImage.asset.url} 
                      alt={project.mainImage.alt || project.title}
                      className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = 'https://via.placeholder.com/400x300?text=Public+Service';
                      }}
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                      <span className="text-gray-500">Public Service Project</span>
                    </div>
                  )}
                  <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-60"></div>
                  
                  {/* Pin marker in top-right corner */}
                  <div className="absolute top-4 right-4 w-8 h-8 bg-red-600 rounded-full flex items-center justify-center shadow-lg transform group-hover:scale-110 transition-transform duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  
                  <h3 className="absolute bottom-0 left-0 right-0 p-4 text-white font-bold text-xl">
                    <Link to={permalink} className="hover:text-gray-200 transition duration-300">
                      {project.title}
                    </Link>
                  </h3>
                </div>
                <div className="p-6 flex-grow flex flex-col bg-white">
                  {projectDate && (
                    <p className="text-sm text-red-600 mb-2">{projectDate}</p>
                  )}
                  {project.description && (
                    <p className="text-gray-600 mb-4 flex-grow">{project.description}</p>
                  )}
                  <Link 
                    to={permalink} 
                    className="inline-block mt-auto py-2 px-4 bg-gray-100 text-gray-800 hover:bg-red-600 hover:text-white rounded-md transition-colors duration-300 font-medium text-center"
                  >
                    {language === 'en' ? 'Learn More' : 'Mëso Më Shumë'}
                  </Link>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="bg-white rounded-lg p-8 text-center border border-gray-200">
          <h3 className="text-xl font-semibold text-gray-800 mb-3">
            {language === 'en' ? 'No public service projects available yet' : 'Nuk ka ende projekte të shërbimit publik të disponueshme'}
          </h3>
          <p className="text-gray-700 max-w-2xl mx-auto">
            {language === 'en' 
              ? 'We are currently developing new public service complex projects. Please check back soon for updates on our community infrastructure initiatives.'
              : 'Aktualisht po zhvillojmë projekte të reja të kompleksit të shërbimit publik. Ju lutemi kontrolloni sërish së shpejti për përditësime mbi iniciativat tona të infrastrukturës komunitare.'}
          </p>
          <button 
            onClick={handleBackClick}
            className="mt-6 bg-red-600 text-white px-6 py-2 rounded-md hover:bg-red-700 transition duration-300"
          >
            {language === 'en' ? 'View All Real Estate Projects' : 'Shiko Të Gjitha Projektet e Pasurive të Paluajtshme'}
          </button>
        </div>
      )}
    </div>
  );
};

export default PublicServiceProjects;