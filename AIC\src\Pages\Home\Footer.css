/* Footer.css */
.Footer-wrapper {
    position: relative;
    padding-top: 350px;
    overflow: hidden;
}

.Footer__investment-banner {
    background-color: white;
    color: black;
    text-align: center;
    padding: 7rem;
    border-radius: 60px;
    position: absolute;
    top: 150px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 4rem);
    max-width: 1500px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    z-index: 10;
}

.Footer__banner-title {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    font-weight: bold;
}

.Footer__invest-button {
    background-color: #e41e26;
    color: white;
    border: none;
    padding: 1.25rem 3.5rem;
    border-radius: 30px;
    font-size: 1.3rem;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.Footer__invest-button:hover {
    background-color: #c51920;
}

.Footer {
    background-color: #e41e26;
    color: white;
    font-family: Arial, sans-serif;
    padding: 12rem 0 4rem;
    position: relative;
}

.Footer__side-image {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 20%;
    background-size: cover;
    background-repeat: no-repeat;
    opacity: 0.5;
    z-index: 1;
}

.Footer__side-image--left {
    left: -5%;
    background-image: url('/public/Asset 3.png');
    background-position: right center;
}

.Footer__side-image--right {
    right: -5%;
    background-image: url('/public/Asset 3.png');
    background-position: left center;
}

.Footer__container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
    z-index: 2;
}

.Footer__content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 3rem;
}

.Footer__section {
    flex: 1;
    min-width: 200px;
    margin-bottom: 2rem;
    padding: 0 1rem;
    text-align: center; /* Center-align the content */
}

.Footer__logo {
    max-width: 100%;
    height: auto;
    display: block;
    margin-bottom: 1.5rem;
    margin-left: auto; /* Center the logo */
    margin-right: auto; /* Center the logo */
}

.Footer__company-description {
    font-size: 1rem;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.Footer__social-icons {
    display: flex;
    gap: 1.5rem;
    justify-content: center; /* Center the social icons */
}

.Footer__icon {
    font-size: 1.4rem;
}

.Footer__section-title {
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.Footer__link-list,
.Footer__contact-list {
    list-style-type: none;
    padding: 0;
    
}


.Footer__link-item,
.Footer__contact-item {
    margin-bottom: 0.75rem;
    font-size: 1rem;
    
}

.Footer__copyright {
    text-align: center;
    margin-top: 3rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.3);
    font-size: 0.9rem;
}


@media (max-width: 1200px) {
    .Footer-wrapper {
        padding-top: 300px;
    }

    .Footer__investment-banner {
        padding: 5rem;
        top: 100px;
    }
}

@media (max-width: 992px) {
    .Footer-wrapper {
        padding-top: 250px;
    }

    .Footer__investment-banner {
        padding: 4rem;
        top: 80px;
    }

    .Footer__banner-title {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .Footer-wrapper {
        padding-top: 200px;
    }

    .Footer__investment-banner {
        padding: 3rem 2rem;
        top: 50px;
        border-radius: 40px;
    }

    .Footer__banner-title {
        font-size: 1.8rem;
    }

    .Footer__invest-button {
        padding: 1rem 2.5rem;
        font-size: 1.1rem;
    }

    .Footer__content {
        flex-direction: column;
    }
    
    .Footer__section {
        margin-bottom: 2.5rem;
    }

    .Footer {
        padding: 8rem 0 3rem;
    }

    .Footer__side-image {
        width: 30%;
        opacity: 0.1;
    }

    .Footer__side-image--left {
        left: -10%;
    }

    .Footer__side-image--right {
        right: -10%;
    }
}

@media (max-width: 576px) {
    .Footer-wrapper {
        padding-top: 180px;
    }

    .Footer__investment-banner {
        padding: 2rem 1.5rem;
        top: 30px;
        border-radius: 30px;
    }

    .Footer__banner-title {
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .Footer__invest-button {
        padding: 0.8rem 2rem;
        font-size: 1rem;
    }

    .Footer__container {
        padding: 0 1rem;
    }

    .Footer__section {
        padding: 0;
    }

    .Footer__side-image {
        width: 20%;
    }
}