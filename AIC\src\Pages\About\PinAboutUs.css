.PinAboutUs {
    background-color: #d32f2f;
    color: white;
    padding: 80px 40px;
    border-radius: 40px;
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
  }
  
  .PinAboutUs-content {
    max-width: 1000px; /* Increased content width */
    margin: 0 auto; /* Center content within the red box */
    z-index: 2;
    text-align: left; /* Ensure text is aligned to the left */
  }
  
  .PinAboutUs-white-block {
    width: 50px; /* Width of the white block */
    height: 5px; /* Height of the white block */
    background-color: white;
    margin-bottom: 10px;
  }
  
  .PinAboutUs h2 {
    font-size: 1.2em;
    font-weight: 400;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 2px;
  }
  
  .PinAboutUs h1 {
    margin: 0;
    font-size: 2.5em;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
  }
  
  .PinAboutUs p {
    font-size: 1.1em;
    line-height: 1.6;
    margin-bottom: 20px;
  }
  
  .PinAboutUs-side-image {
    position: absolute;
    top: 50%;
    height: 120%;
    transform: translateY(-50%);
    z-index: 1;
    opacity: 0.3; /* Maintain opacity to not overpower the text */
  }
  
  .PinAboutUs-left-image {
    left: -25%; /* Position it such that only 50% of the image is inside the block */
    width: 50%;
  }
  
  .PinAboutUs-right-image {
    right: -25%; /* Position it such that only 50% of the image is inside the block */
    width: 50%;
  }
  
  @media (max-width: 1023px) {
    .PinAboutUs {
      padding: 60px 30px;
      border-radius: 30px;
    }
  
    .PinAboutUs-content {
      max-width: 90%;
      margin: 0 auto;
    }
  
    .PinAboutUs-side-image {
      height: 100%;
      width: auto;
    }
  
    .PinAboutUs h1 {
      font-size: 2em;
    }
  
    .PinAboutUs p {
      font-size: 1em;
    }
  }
  
  @media (max-width: 767px) {
    .PinAboutUs {
      padding: 40px 20px;
      border-radius: 20px;
    }
  
    .PinAboutUs-content {
      max-width: 100%;
      margin: 0 auto;
    }
  
    .PinAboutUs h1 {
      font-size: 1.6em;
    }
  
    .PinAboutUs p {
      font-size: 0.9em;
    }
  
    .PinAboutUs-side-image {
      display: none;
    }
  }
  