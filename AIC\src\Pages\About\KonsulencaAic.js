import React, { useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useLanguage } from '../../components/languageUtils';
import SEO from '../../components/SEO';
import { seoTitles } from '../../config/seoTitles';
import { motion, useAnimation } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Building2, Shield, HandshakeIcon, PiggyBank } from 'lucide-react';

const AnimatedSection = ({ children, className }) => {
  const controls = useAnimation();
  const [ref, inView] = useInView({
    threshold: 0.2,
    triggerOnce: true
  });

  useEffect(() => {
    if (inView) {
      controls.start('visible');
    }
  }, [controls, inView]);

  return (
    <motion.section
      ref={ref}
      initial="hidden"
      animate={controls}
      variants={{
        hidden: { opacity: 0, y: 50 },
        visible: {
          opacity: 1,
          y: 0,
          transition: {
            duration: 0.6,
            ease: "easeOut"
          }
        }
      }}
      className={className}
    >
      {children}
    </motion.section>
  );
};

const AICConsultancy = ({ loading }) => {
  const location = useLocation();
  const { language, translations } = useLanguage();
  const seoData = seoTitles.consultancy[language];

  const aicLinks = [
    { name: translations[language].submenus.aboutUs.visionMission, path: `/${language}/${translations[language].routes['about-us']}/${translations[language].routes['vision-and-mission']}` },
    { name: translations[language].submenus.aboutUs.whyChooseAIC, path: `/${language}/${translations[language].routes['about-us']}/${translations[language].routes['why-choose-aic']}` },
    { name: translations[language].submenus.aboutUs.lawsRegulations, path: `/${language}/${translations[language].routes['about-us']}/${translations[language].routes['law-and-regulations']}` },
    { name: translations[language].submenus.aboutUs.consultancy, path: `/${language}/${translations[language].routes['about-us']}/${translations[language].routes['consultancy']}` },
  ];

  const content = {
    en: {
      title: "ALBANIAN INVESTMENT CORPORATION",
      subtitle: "AIC CONSULTANCY",
      aboutAlbania: {
        title: "About Albania",
        content: [
          "Albania is experiencing robust economic growth, driven by strategic investments in infrastructure, energy, and tourism. The government's pro-business policies and favorable tax regime have made it an attractive destination for foreign investors. With a young and skilled workforce, Albania offers significant opportunities in sectors such as agriculture, technology, and renewable energy. The country's strategic location further enhances its appeal, providing easy access to European markets and facilitating international trade. As a result, Albania is poised to become a key player in the regional economy, offering abundant opportunities for entrepreneurs and investors alike.",
          "Furthermore this country captivates with its stunning natural beauty, featuring pristine beaches along the Adriatic and Ionian Seas, majestic mountains, and a rich tapestry of historical and cultural landmarks.",
          "Albania is rapidly emerging as a hub for entrepreneurs, offering a supportive business environment, attractive investment opportunities, and a young, dynamic workforce eager to innovate."
        ]
      },
      sections: [
        {
          title: "Public-Private Partnerships and Infrastructure Development",
          content: [
            "AIC encourages public-private partnerships, leveraging modern technology and sharing investment risks. It focuses on transforming state properties and buildings, through collaborative efforts with public institutions and private partners. This approach acts as a strategic facilitator between the government, private sector, and other stakeholders, driving economic development aligned with state policies and objectives.",
            "Build new facilities and improve workspaces, boosting productivity and standards. Key aspects defining AIC in its role as the developer and manager of State Properties, which no other institution in Albania enjoys, include:"
          ],
          keyPoints: [
            {
              title: "Legal Authority",
              description: "AIC has the necessary legal framework and authority to identify state properties with development potential, plan their development, construction, or reconstruction, and oversee these projects."
            },
            {
              title: "Unique Role",
              description: "This new practice for the country involves potential risks, such as coordination issues with state institutions, delays in property consolidation and transfer procedures, and challenges in managing complex logistics."
            }
          ],
          strategies: [
            {
              title: "Enhanced Coordination with State Institutions",
              description: "As a state-owned company, AIC can establish strong communication channels with relevant state institutions to ensure compliance and smooth coordination throughout the project's lifecycle."
            },
            {
              title: "Simplification of Asset Consolidation and Transfer",
              description: "To efficiently consolidate and transfer assets, minimizing delays and bureaucratic obstacles, AIC will enter into agreements with state institutions owning the state assets to be developed."
            }
          ]
        }
      ],
      incentives: {
        title: "Incentives and Financial Benefits",
        description: "AIC offers a range of tax reductions, subsidies, and streamlined administrative procedures designed to facilitate a smooth and efficient investment process. These incentives are structured to enhance the attractiveness of Albania as a business destination, reducing operational costs and increasing profitability for investors."
      }
    },
    sq: {
      title: "INVESTIMI JUAJ MBËSHTETET NGA KISH-i!",
      subtitle: "Konsulenca KISH",
      aboutAlbania: {
        title: "Rreth Shqipërisë",
        content: [
          "Shqipëria po përjeton një rritje të fuqishme ekonomike, e nxitur nga investimet strategjike në infrastrukturë, energji dhe turizëm. Politikat pro-biznes dhe regjimi i favorshëm tatimor i qeverisë e kanë bërë atë një destinacion tërheqës për investitorët e huaj. Me një fuqi punëtore të re dhe të kualifikuar, Shqipëria ofron mundësi të konsiderueshme në sektorë të tillë, si: bujqësia, teknologjia dhe energjia e rinovueshme. Vendndodhja strategjike e vendit e bën atë edhe më tërheqë, duke ofruar akses të lehtë në tregjet evropiane dhe duke lehtësuar tregtinë ndërkombëtare. Si rezultat, Shqipëria është në prag të shndërrimit në një lojtar kyç në ekonominë rajonale, duke ofruar mundësi të bollshme për sipërmarrësit dhe investitorët.",
          "Gjithashtu, ky vend magjeps me bukurinë e tij natyrore të mahnitshme, duke përfshirë plazhe të virgjëra përgjatë detit Adriatik dhe Jon, male madhështore dhe një pasuri të gjerë të trashëgimisë historike dhe kulturore.",
          "Shqipëria po bëhet me shpejtësi një qendër për sipërmarrësit, duke ofruar një mjedis mbështetës për biznesin, mundësi tërheqëse investimi dhe një fuqi punëtore të re dhe dinamike, të gatshme për risi."
        ]
      },
      sections: [
        {
          title: "Partneritete publike-private dhe zhvillimi i infrastrukturës",
          content: [
            "KISH-i inkurajon partneritetet publike-private, duke shfrytëzuar teknologjinë moderne dhe ndarjen e rreziqeve të investimit. Ajo fokusohet në transformimin e pronave dhe ndërtesave shtetërore, përmes përpjekjeve të përbashkëta me institucionet publike dhe partnerët privatë. Kjo qasje vepron si një lehtësues strategjik midis qeverisë, sektorit privat dhe aktorëve të tjerë, duke nxitur zhvillimin ekonomik në përputhje me politikat dhe objektivat shtetërore.",
            "Ndërtoni objekte të reja dhe përmirësoni hapësirat e punës, duke rritur produktivitetin dhe standardet. Aspektet kyçe të KISH-it në rolin e tij si zhvillues dhe menaxher i pronave shtetërore (rol që nuk e ka asnjë institucion tjetër në Shqipëri nuk e gëzon), janë:"
          ],
          keyPoints: [
            {
              title: "Autoriteti ligjor",
              description: "KISH-i ka kuadrin ligjor dhe autoritetin e nevojshëm për të identifikuar pronat shtetërore me potencial zhvillimi, për të planifikuar zhvillimin e tyre, ndërtimin ose rindërtimin, dhe për të mbikëqyrur këto projekte."
            },
            {
              title: "Roli unik",
              description: "Kjo praktikë e re për vendin përfshin rreziqe të mundshme, të tilla si çështje koordinimi me institucionet shtetërore, vonesa në procedurat e konsolidimit dhe transferimit të pronave, si dhe sfida në menaxhimin e logjistikës komplekse."
            }
          ],
          strategies: [
            {
              title: "Koordinimi i përmirësuar me institucionet shtetërore",
              description: "Si një kompani shtetërore, KISH-i mund të krijojë kanale të forta komunikimi me institucionet përkatëse shtetërore për të siguruar përputhshmëri dhe koordinim të qetë gjatë gjithë ciklit të projektit."
            },
            {
              title: "Thjeshtimi i konsolidimit dhe transferimit të aseteve",
              description: "Për të konsoliduar dhe transferuar asetet në mënyrë efikase, duke minimizuar vonesat dhe pengesat burokratike, KISH-i do të hyjë në marrëveshje me institucionet shtetërore, të cilat zotërojnë asetet që do të zhvillohen."
            }
          ]
        }
      ],
      incentives: {
        title: "Incentiva dhe përfitime financiare",
        description: "KISH-i ofron një gamë të gjerë uljesh tatimore, subvencione dhe procedura të thjeshtuara administrative të dizajnuara për të lehtësuar një proces investimi të qetë dhe efikas. Këto stimuj janë strukturuar për të rritur tërheqjen e Shqipërisë si një destinacion biznesi, duke ulur kostot operative dhe duke rritur përfitimin për investitorët."
      }
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  return (
    <>
      <SEO
        customTitle={seoData.title}
        customDescription={seoData.description}
        language={language}
        ogType="website"
      />
      <motion.div
        className="max-w-7xl mx-auto px-5 py-10"
        initial="hidden"
        animate={loading ? "hidden" : "visible"}
        variants={containerVariants}
      >
      <div className="flex flex-col lg:flex-row justify-between gap-10">
        <div className="flex-1">
          {/* Header Section */}
          <AnimatedSection className="mb-10">
            <div className="flex flex-col items-start">
              <div className="flex items-center mb-5">
                <div className="w-10 h-1.5 bg-red-600 mr-2.5"></div>
                <h3 className="text-xl font-bold text-gray-800 uppercase">{content[language].subtitle}</h3>
              </div>
              <h2 className="text-4xl font-bold text-red-600 mb-6">
                {content[language].title}
              </h2>
            </div>
          </AnimatedSection>

          {/* Main Content Wrapper */}
          <div className="space-y-8">
            {/* About Albania Section - New Design */}
            <AnimatedSection>
              <div className="bg-white rounded-xl overflow-hidden shadow-lg">
                <div className="bg-gradient-to-r from-red-600 to-red-700 p-6">
                  <h3 className="text-2xl font-bold text-white">
                    {content[language].aboutAlbania.title}
                  </h3>
                </div>
                <div className="p-8 space-y-6">
                  {content[language].aboutAlbania.content.map((paragraph, index) => (
                    <p key={index} className="text-gray-700 leading-relaxed">
                      {paragraph}
                    </p>
                  ))}
                </div>
              </div>
            </AnimatedSection>

            {/* Sections */}
            {content[language].sections.map((section, sectionIndex) => (
              <AnimatedSection key={sectionIndex}>
                <div className="bg-white rounded-xl shadow-lg p-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-6">
                    {section.title}
                  </h3>
                  <div className="space-y-6">
                    {section.content.map((paragraph, index) => (
                      <p key={index} className="text-gray-700 leading-relaxed">
                        {paragraph}
                      </p>
                    ))}
                    
                    {/* Key Points */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8">
                      {section.keyPoints.map((point, index) => (
                        <div 
                          key={index}
                          className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border-l-4 border-red-600"
                        >
                          <h4 className="text-lg font-bold text-gray-800 mb-3">{point.title}</h4>
                          <p className="text-gray-600">{point.description}</p>
                        </div>
                      ))}
                    </div>

                    {/* Strategies */}
                    <div className="mt-8">
                      <h4 className="text-xl font-bold text-gray-800 mb-6">
                        {language === 'en' ? 'Strategies' : 'Strategjitë'}
                      </h4>
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {section.strategies.map((strategy, index) => (
                          <div 
                            key={index}
                            className="bg-white rounded-xl shadow-md p-6 border border-gray-100"
                          >
                            <h5 className="text-lg font-bold text-gray-800 mb-3">{strategy.title}</h5>
                            <p className="text-gray-600">{strategy.description}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </AnimatedSection>
            ))}

            {/* Incentives Section */}
            <AnimatedSection>
              <div className="bg-white rounded-xl shadow-lg p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">
                  {content[language].incentives.title}
                </h3><p className="text-gray-700 leading-relaxed">
                  {content[language].incentives.description}
                </p>
              </div>
            </AnimatedSection>
          </div>
        </div>

        {/* Sidebar - Keeping Original Design */}
        <aside className="w-full lg:w-72">
                 <nav className="rounded-lg p-6">
                   <h3 className="text-2xl font-bold text-gray-900 mb-6 uppercase">
                     {translations[language].common.quickLinks}
                   </h3>
                   <ul className="space-y-4">
                     {aicLinks.map((link) => (
                       <li key={link.path}>
                         <Link 
                           to={link.path}
                           className={`block py-5 px-6 rounded-lg transition-all duration-300 ease-in-out text-lg font-medium
                             ${location.pathname === link.path 
                               ? 'bg-red-600 text-white shadow-md' 
                               : 'text-gray-600 hover:bg-gray-200'}`}
                         >
                           {link.name}
                         </Link>
                       </li>
                     ))}
                   </ul>
                 </nav>
               </aside>
      </div>
    </motion.div>
    </>
  );
};

export default AICConsultancy;