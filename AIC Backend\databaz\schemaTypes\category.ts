// migrations/categoryToArray.js
import client from 'part:@sanity/base/client'

// This uses your current authentication - no token needed
const sanityClient = client.withConfig({apiVersion: '2021-06-07'})

async function migrateCategories() {
  try {
    console.log('Starting migration...')
    // Fetch all project posts with a string category
    const documents = await sanityClient.fetch(
      `*[_type == "projectsPost" && defined(category) && typeof(category) == "string"]`
    )
    
    console.log(`Found ${documents.length} documents to migrate`)
    
    // Process each document
    let migrated = 0
    for (const doc of documents) {
      try {
        console.log(`Migrating document: ${doc.title || 'Untitled'}`)
        // Convert string to array
        await sanityClient
          .patch(doc._id)
          .set({ 
            category: doc.category ? [doc.category] : [] 
          })
          .commit()
        
        migrated++
        console.log(`Successfully migrated (${migrated}/${documents.length})`)
      } catch (err) {
        console.error(`Failed to migrate document ${doc._id}:`, err)
      }
    }
    
    console.log(`Migration completed: ${migrated} documents updated`)
  } catch (err) {
    console.error('Migration failed:', err)
  }
}

// Run the migration
migrateCategories()